T7474 000:009.216   SEGGER J-Link V7.98a Log File
T7474 000:009.324   DLL Compiled: Jul 19 2024 14:57:58
T7474 000:009.345   Logging started @ 2025-07-31 03:21
T7474 000:009.365   Process: D:\RYH\updedate_app\keil5\UV4\UV4.exe
T7474 000:009.387 - 9.384ms
T7474 000:009.410 JLINK_SetWarnOut<PERSON>andler(...)
T7474 000:009.429 - 0.020ms
T7474 000:009.453 JLINK_OpenEx(...)
T7474 000:011.591   Firmware: J-Link V9 compiled Dec  8 2023 20:16:22
T7474 000:013.660   Firmware: J-Link V9 compiled Dec  8 2023 20:16:22
T7474 000:013.792   Decompressing FW timestamp took 81 us
T7474 000:021.896   Hardware: V9.70
T7474 000:021.929   S/N: 602721283
T7474 000:021.957   OEM: SEGGER
T7474 000:021.984   Feature(s): GDB, R<PERSON>, FlashB<PERSON>, FlashDL, J<PERSON><PERSON>
T7474 000:024.069   Bootloader: (Could not read)
T7474 000:026.292   TELNET listener socket opened on port 19021
T7474 000:026.631   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T7474 000:026.753   WEBSRV Webserver running on local port 19080
T7474 000:027.054   Looking for J-Link GUI Server exe at: D:\RYH\updedate_app\keil5\ARM\Segger\JLinkGUIServer.exe
T7474 000:027.423   Looking for J-Link GUI Server exe at: D:\RYH\updedate_app\jlink_v720\JLink_V798a\JLinkGUIServer.exe
T7474 000:027.574   Forking J-Link GUI Server: D:\RYH\updedate_app\jlink_v720\JLink_V798a\JLinkGUIServer.exe
T7474 000:053.112   J-Link GUI Server info: "J-Link GUI server V7.98a "
T7474 000:056.976 - 47.518ms returns "O.K."
T7474 000:057.010 JLINK_GetEmuCaps()
T7474 000:057.031 - 0.019ms returns 0xB9FF7BBF
T7474 000:057.053 JLINK_TIF_GetAvailable(...)
T7474 000:057.711 - 0.657ms
T7474 000:057.742 JLINK_SetErrorOutHandler(...)
T7474 000:057.761 - 0.019ms
T7474 000:057.801 JLINK_ExecCommand("ProjectFile = "D:\Qian\00_dian_sai\now_project_ti\ti_template\keil\JLinkSettings.ini"", ...). 
T7474 000:068.211   Ref file found at: D:\RYH\updedate_app\keil5\ARM\Segger\JLinkDevices.ref
T7474 000:068.563   REF file references invalid XML file: D:\RYH\updedate_app\jlink_v720\JLink_V798a\JLinkDevices.xml
T7474 000:073.732   Device "PAC5210" selected.
T7474 000:074.217 - 16.417ms returns 0x00
T7474 000:076.526 JLINK_ExecCommand("Device = MSPM0G3507", ...). 
T7474 000:078.861   Device "PAC5210" selected.
T7474 000:079.313 - 2.750ms returns 0x00
T7474 000:079.337 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T7474 000:079.359   ERROR: Unknown command
T7474 000:079.387 - 0.029ms returns 0x01
T7474 000:079.410 JLINK_GetHardwareVersion()
T7474 000:079.430 - 0.021ms returns 97000
T7474 000:079.450 JLINK_GetDLLVersion()
T7474 000:079.469 - 0.018ms returns 79801
T7474 000:079.489 JLINK_GetOEMString(...)
T7474 000:079.509 JLINK_GetFirmwareString(...)
T7474 000:079.528 - 0.019ms
T7474 000:086.832 JLINK_GetDLLVersion()
T7474 000:086.857 - 0.025ms returns 79801
T7474 000:086.878 JLINK_GetCompileDateTime()
T7474 000:086.896 - 0.018ms
T7474 000:089.015 JLINK_GetFirmwareString(...)
T7474 000:089.040 - 0.025ms
T7474 000:091.129 JLINK_GetHardwareVersion()
T7474 000:091.155 - 0.026ms returns 97000
T7474 000:093.316 JLINK_GetSN()
T7474 000:093.342 - 0.026ms returns 602721283
T7474 000:095.512 JLINK_GetOEMString(...)
T7474 000:100.044 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T7474 000:102.498 - 2.453ms returns 0x00
T7474 000:102.557 JLINK_HasError()
T7474 000:102.624 JLINK_SetSpeed(5000)
T7474 000:103.235 - 0.613ms
T7474 000:103.262 JLINK_GetId()
T7474 000:107.368   Found SW-DP with ID 0x6BA02477
T7474 000:115.629   DPIDR: 0x6BA02477
T7474 000:117.787   CoreSight SoC-400 or earlier
T7474 000:120.051   Scanning AP map to find all available APs
T7474 000:126.921   AP[5]: Stopped AP scan as end of AP map has been reached
T7474 000:129.052   AP[0]: AHB-AP (IDR: 0x84770001)
T7474 000:131.104   AP[1]: MEM-AP (IDR: 0x002E0001)
T7474 000:133.351   AP[2]: JTAG-AP (IDR: 0x002E0000)
T7474 000:135.483   AP[3]: MEM-AP (IDR: 0x002E0003)
T7474 000:137.600   AP[4]: MEM-AP (IDR: 0x002E0002)
T7474 000:139.978   Iterating through AP map to find AHB-AP to use
T7474 000:143.681   AP[0]: Core found
T7474 000:145.753   AP[0]: AHB-AP ROM base: 0xF0000000
T7474 000:148.903   CPUID register: 0x410CC601. Implementer code: 0x41 (ARM)
T7474 000:150.956   Found Cortex-M0 r0p1, Little endian.
T7474 000:152.287   -- Max. mem block: 0x00010C20
T7474 000:153.559   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7474 000:154.303   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T7474 000:155.177   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:158.306   FPUnit: 4 code (BP) slots and 0 literal slots
T7474 000:158.342   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T7474 000:159.150   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7474 000:159.897   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:160.638   CPU_WriteMem(4 bytes @ 0x********)
T7474 000:163.567   CoreSight components:
T7474 000:165.673   ROMTbl[0] @ F0000000
T7474 000:165.709   CPU_ReadMem(64 bytes @ 0xF0000000)
T7474 000:166.651   CPU_ReadMem(32 bytes @ 0xE00FFFE0)
T7474 000:170.447   [0][0]: E00FF000 CID B105100D PID 000BB4C0 ROM Table
T7474 000:172.953   ROMTbl[1] @ E00FF000
T7474 000:172.989   CPU_ReadMem(64 bytes @ 0xE00FF000)
T7474 000:174.011   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T7474 000:176.942   [1][0]: E000E000 CID B105E00D PID 000BB008 SCS
T7474 000:176.978   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T7474 000:180.181   [1][1]: ******** CID B105E00D PID 000BB00A DWT
T7474 000:180.217   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T7474 000:183.135   [1][2]: ******** CID B105E00D PID 000BB00B FPB
T7474 000:183.171   CPU_ReadMem(32 bytes @ 0x40402FE0)
T7474 000:186.100   [0][2]: 40402000 CID B105900D PID 001BB932 MTB-M0+
T7474 000:186.857 - 83.594ms returns 0x6BA02477
T7474 000:186.892 JLINK_GetDLLVersion()
T7474 000:186.911 - 0.019ms returns 79801
T7474 000:186.933 JLINK_CORE_GetFound()
T7474 000:186.952 - 0.019ms returns 0x60000FF
T7474 000:186.974 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T7474 000:186.994   Value=0xF0000000
T7474 000:187.022 - 0.048ms returns 0
T7474 000:189.471 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T7474 000:189.497   Value=0xF0000000
T7474 000:189.525 - 0.054ms returns 0
T7474 000:189.545 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T7474 000:189.564   Value=0x00000000
T7474 000:189.592 - 0.047ms returns 0
T7474 000:189.613 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T7474 000:189.649   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T7474 000:190.438   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T7474 000:190.472 - 0.859ms returns 16 (0x10)
T7474 000:190.494 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T7474 000:190.513   Value=0x40402000
T7474 000:190.541 - 0.047ms returns 0
T7474 000:190.561 JLINK_CORE_GetFound()
T7474 000:190.580 - 0.019ms returns 0x60000FF
T7474 000:190.600 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T7474 000:190.618   Value=0x00000000
T7474 000:190.646 - 0.046ms returns 0
T7474 000:190.666 JLINK_ReadMemEx(0xE0040FF0, 0x10 Bytes, Flags = 0x02000004)
T7474 000:190.688   CPU_ReadMem(16 bytes @ 0xE0040FF0)
T7474 000:191.423   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T7474 000:191.457 - 0.790ms returns 16 (0x10)
T7474 000:191.479 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T7474 000:191.497   Value=0xE0000000
T7474 000:191.525 - 0.047ms returns 0
T7474 000:191.545 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T7474 000:191.564   Value=0x********
T7474 000:191.592 - 0.046ms returns 0
T7474 000:191.612 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T7474 000:191.631   Value=0x********
T7474 000:191.659 - 0.046ms returns 0
T7474 000:191.679 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T7474 000:191.697   Value=0xE000E000
T7474 000:191.725 - 0.046ms returns 0
T7474 000:191.745 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T7474 000:191.764   Value=0xE000EDF0
T7474 000:191.792 - 0.046ms returns 0
T7474 000:191.812 JLINK_GetDebugInfo(0x01 = Unknown)
T7474 000:191.830   Value=0x00000000
T7474 000:191.858 - 0.046ms returns 0
T7474 000:191.878 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T7474 000:191.902   CPU_ReadMem(4 bytes @ 0xE000ED00)
T7474 000:192.632   Data:  01 C6 0C 41
T7474 000:192.671   Debug reg: CPUID
T7474 000:192.699 - 0.820ms returns 1 (0x1)
T7474 000:192.721 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T7474 000:192.739   Value=0x00000000
T7474 000:192.767 - 0.046ms returns 0
T7474 000:192.788 JLINK_HasError()
T7474 000:192.808 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T7474 000:192.827 - 0.019ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T7474 000:192.847 JLINK_Reset()
T7474 000:192.875   CPU is running
T7474 000:192.903   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T7474 000:193.706   CPU is running
T7474 000:193.739   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7474 000:196.548   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T7474 000:200.123   Reset: Reset device via AIRCR.SYSRESETREQ.
T7474 000:200.158   CPU is running
T7474 000:200.187   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T7474 000:255.713   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7474 000:256.427   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7474 000:259.989   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7474 000:267.896   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7474 000:271.401   CPU_WriteMem(4 bytes @ 0x********)
T7474 000:272.169   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T7474 000:272.881   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:273.642   CPU_WriteMem(4 bytes @ 0x********)
T7474 000:274.403 - 81.555ms
T7474 000:274.466 JLINK_Halt()
T7474 000:274.489 - 0.023ms returns 0x00
T7474 000:274.509 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T7474 000:274.531   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7474 000:275.257   Data:  03 00 03 00
T7474 000:275.289   Debug reg: DHCSR
T7474 000:275.317 - 0.807ms returns 1 (0x1)
T7474 000:275.347 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T7474 000:275.369   Debug reg: DHCSR
T7474 000:275.618   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T7474 000:276.393 - 1.046ms returns 0 (0x00000000)
T7474 000:276.419 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T7474 000:276.438   Debug reg: DEMCR
T7474 000:276.468   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7474 000:277.163 - 0.743ms returns 0 (0x00000000)
T7474 000:286.379 JLINK_GetHWStatus(...)
T7474 000:287.049 - 0.670ms returns 0
T7474 000:293.746 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T7474 000:293.771 - 0.025ms returns 0x04
T7474 000:293.791 JLINK_GetNumBPUnits(Type = 0xF0)
T7474 000:293.810 - 0.018ms returns 0x2000
T7474 000:293.830 JLINK_GetNumWPUnits()
T7474 000:293.849 - 0.018ms returns 2
T7474 000:300.949 JLINK_GetSpeed()
T7474 000:300.975 - 0.026ms returns 4000
T7474 000:305.252 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T7474 000:305.282   CPU_ReadMem(4 bytes @ 0xE000E004)
T7474 000:306.034   Data:  00 00 00 00
T7474 000:306.069 - 0.817ms returns 1 (0x1)
T7474 000:306.094 JLINK_Halt()
T7474 000:306.113 - 0.019ms returns 0x00
T7474 000:306.133 JLINK_IsHalted()
T7474 000:306.153 - 0.019ms returns TRUE
T7474 000:309.649 JLINK_WriteMem(0x20200000, 0x294 Bytes, ...)
T7474 000:309.671   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T7474 000:309.859   CPU_WriteMem(660 bytes @ 0x20200000)
T7474 000:312.749 - 3.100ms returns 0x294
T7474 000:312.790 JLINK_HasError()
T7474 000:312.819 JLINK_WriteReg(R0, 0x00000000)
T7474 000:312.839 - 0.028ms returns 0
T7474 000:312.859 JLINK_WriteReg(R1, 0x01F78A40)
T7474 000:312.877 - 0.018ms returns 0
T7474 000:312.897 JLINK_WriteReg(R2, 0x00000001)
T7474 000:312.916 - 0.018ms returns 0
T7474 000:312.936 JLINK_WriteReg(R3, 0x00000000)
T7474 000:312.955 - 0.018ms returns 0
T7474 000:312.974 JLINK_WriteReg(R4, 0x00000000)
T7474 000:312.993 - 0.018ms returns 0
T7474 000:313.012 JLINK_WriteReg(R5, 0x00000000)
T7474 000:313.031 - 0.018ms returns 0
T7474 000:313.050 JLINK_WriteReg(R6, 0x00000000)
T7474 000:313.069 - 0.018ms returns 0
T7474 000:313.089 JLINK_WriteReg(R7, 0x00000000)
T7474 000:313.107 - 0.018ms returns 0
T7474 000:313.130 JLINK_WriteReg(R8, 0x00000000)
T7474 000:313.149 - 0.022ms returns 0
T7474 000:313.168 JLINK_WriteReg(R9, 0x20200290)
T7474 000:313.189 - 0.020ms returns 0
T7474 000:313.209 JLINK_WriteReg(R10, 0x00000000)
T7474 000:313.227 - 0.018ms returns 0
T7474 000:313.247 JLINK_WriteReg(R11, 0x00000000)
T7474 000:313.266 - 0.018ms returns 0
T7474 000:313.285 JLINK_WriteReg(R12, 0x00000000)
T7474 000:313.304 - 0.018ms returns 0
T7474 000:313.323 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:313.342 - 0.018ms returns 0
T7474 000:313.362 JLINK_WriteReg(R14, 0x20200001)
T7474 000:313.380 - 0.018ms returns 0
T7474 000:313.404 JLINK_WriteReg(R15 (PC), 0x20200038)
T7474 000:313.422 - 0.022ms returns 0
T7474 000:313.442 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:313.461 - 0.018ms returns 0
T7474 000:313.481 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:313.503 - 0.022ms returns 0
T7474 000:313.523 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:313.542 - 0.018ms returns 0
T7474 000:313.562 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:313.580 - 0.018ms returns 0
T7474 000:313.601 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:313.629   CPU_ReadMem(4 bytes @ 0x20200000)
T7474 000:314.416   CPU_WriteMem(4 bytes @ 0x20200000)
T7474 000:315.161   CPU_ReadMem(4 bytes @ 0x20200000)
T7474 000:315.909   CPU_WriteMem(4 bytes @ 0x20200000)
T7474 000:316.652   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:317.417 - 3.816ms returns 0x00000001
T7474 000:317.443 JLINK_Go()
T7474 000:317.464   CPU_WriteMem(2 bytes @ 0x20200000)
T7474 000:318.157   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:318.883   CPU_WriteMem(4 bytes @ 0x********)
T7474 000:319.649   CPU_WriteMem(4 bytes @ 0xE0002008)
T7474 000:319.684   CPU_WriteMem(4 bytes @ 0xE000200C)
T7474 000:319.713   CPU_WriteMem(4 bytes @ 0xE0002010)
T7474 000:319.740   CPU_WriteMem(4 bytes @ 0xE0002014)
T7474 000:320.934   CPU_WriteMem(4 bytes @ 0xE0001004)
T7474 000:326.580   Memory map 'after startup completion point' is active
T7474 000:326.614 - 9.170ms
T7474 000:326.639 JLINK_IsHalted()
T7474 000:329.359   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:330.142 - 3.502ms returns TRUE
T7474 000:330.172 JLINK_ReadReg(R15 (PC))
T7474 000:330.192 - 0.020ms returns 0x20200000
T7474 000:330.213 JLINK_ClrBPEx(BPHandle = 0x00000001)
T7474 000:330.232 - 0.018ms returns 0x00
T7474 000:330.253 JLINK_ReadReg(R0)
T7474 000:330.272 - 0.018ms returns 0x00000000
T7474 000:331.224 JLINK_HasError()
T7474 000:331.251 JLINK_WriteReg(R0, 0x00000000)
T7474 000:331.272 - 0.020ms returns 0
T7474 000:331.292 JLINK_WriteReg(R1, 0x00000400)
T7474 000:331.311 - 0.018ms returns 0
T7474 000:331.331 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:331.350 - 0.018ms returns 0
T7474 000:331.369 JLINK_WriteReg(R3, 0x00000000)
T7474 000:331.388 - 0.018ms returns 0
T7474 000:331.411 JLINK_WriteReg(R4, 0x00000000)
T7474 000:331.431 - 0.019ms returns 0
T7474 000:331.451 JLINK_WriteReg(R5, 0x00000000)
T7474 000:331.469 - 0.018ms returns 0
T7474 000:331.489 JLINK_WriteReg(R6, 0x00000000)
T7474 000:331.508 - 0.018ms returns 0
T7474 000:331.528 JLINK_WriteReg(R7, 0x00000000)
T7474 000:331.546 - 0.018ms returns 0
T7474 000:331.566 JLINK_WriteReg(R8, 0x00000000)
T7474 000:331.584 - 0.018ms returns 0
T7474 000:331.604 JLINK_WriteReg(R9, 0x20200290)
T7474 000:331.623 - 0.018ms returns 0
T7474 000:331.642 JLINK_WriteReg(R10, 0x00000000)
T7474 000:331.661 - 0.018ms returns 0
T7474 000:331.681 JLINK_WriteReg(R11, 0x00000000)
T7474 000:331.699 - 0.018ms returns 0
T7474 000:331.719 JLINK_WriteReg(R12, 0x00000000)
T7474 000:331.738 - 0.018ms returns 0
T7474 000:331.758 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:331.777 - 0.019ms returns 0
T7474 000:331.796 JLINK_WriteReg(R14, 0x20200001)
T7474 000:331.815 - 0.018ms returns 0
T7474 000:331.835 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:331.854 - 0.018ms returns 0
T7474 000:331.874 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:331.892 - 0.018ms returns 0
T7474 000:331.912 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:331.931 - 0.018ms returns 0
T7474 000:331.951 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:331.970 - 0.019ms returns 0
T7474 000:332.027 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:332.046 - 0.019ms returns 0
T7474 000:332.067 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:332.086 - 0.019ms returns 0x00000002
T7474 000:332.106 JLINK_Go()
T7474 000:332.132   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:335.734 - 3.627ms
T7474 000:335.761 JLINK_IsHalted()
T7474 000:338.488   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:339.277 - 3.516ms returns TRUE
T7474 000:339.304 JLINK_ReadReg(R15 (PC))
T7474 000:339.324 - 0.020ms returns 0x20200000
T7474 000:339.344 JLINK_ClrBPEx(BPHandle = 0x00000002)
T7474 000:339.363 - 0.018ms returns 0x00
T7474 000:339.383 JLINK_ReadReg(R0)
T7474 000:339.402 - 0.019ms returns 0x00000001
T7474 000:339.426 JLINK_HasError()
T7474 000:339.446 JLINK_WriteReg(R0, 0x00000000)
T7474 000:339.465 - 0.019ms returns 0
T7474 000:339.485 JLINK_WriteReg(R1, 0x00000400)
T7474 000:339.504 - 0.018ms returns 0
T7474 000:339.523 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:339.542 - 0.018ms returns 0
T7474 000:339.562 JLINK_WriteReg(R3, 0x00000000)
T7474 000:339.580 - 0.018ms returns 0
T7474 000:339.600 JLINK_WriteReg(R4, 0x00000000)
T7474 000:339.619 - 0.018ms returns 0
T7474 000:339.639 JLINK_WriteReg(R5, 0x00000000)
T7474 000:339.657 - 0.018ms returns 0
T7474 000:339.677 JLINK_WriteReg(R6, 0x00000000)
T7474 000:339.696 - 0.018ms returns 0
T7474 000:339.715 JLINK_WriteReg(R7, 0x00000000)
T7474 000:339.734 - 0.018ms returns 0
T7474 000:339.754 JLINK_WriteReg(R8, 0x00000000)
T7474 000:339.772 - 0.018ms returns 0
T7474 000:339.792 JLINK_WriteReg(R9, 0x20200290)
T7474 000:339.811 - 0.018ms returns 0
T7474 000:339.831 JLINK_WriteReg(R10, 0x00000000)
T7474 000:339.849 - 0.018ms returns 0
T7474 000:339.869 JLINK_WriteReg(R11, 0x00000000)
T7474 000:339.888 - 0.018ms returns 0
T7474 000:339.907 JLINK_WriteReg(R12, 0x00000000)
T7474 000:339.926 - 0.018ms returns 0
T7474 000:339.946 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:339.965 - 0.018ms returns 0
T7474 000:339.984 JLINK_WriteReg(R14, 0x20200001)
T7474 000:340.003 - 0.018ms returns 0
T7474 000:340.023 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:340.042 - 0.018ms returns 0
T7474 000:340.066 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:340.085 - 0.018ms returns 0
T7474 000:340.105 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:340.124 - 0.018ms returns 0
T7474 000:340.143 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:340.162 - 0.018ms returns 0
T7474 000:340.182 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:340.201 - 0.018ms returns 0
T7474 000:340.221 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:340.240 - 0.019ms returns 0x00000003
T7474 000:340.259 JLINK_Go()
T7474 000:340.281   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:343.719 - 3.459ms
T7474 000:343.746 JLINK_IsHalted()
T7474 000:344.534 - 0.787ms returns FALSE
T7474 000:344.561 JLINK_HasError()
T7474 000:356.424 JLINK_IsHalted()
T7474 000:359.373   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:360.170 - 3.744ms returns TRUE
T7474 000:360.256 JLINK_ReadReg(R15 (PC))
T7474 000:360.316 - 0.060ms returns 0x20200000
T7474 000:360.361 JLINK_ClrBPEx(BPHandle = 0x00000003)
T7474 000:360.405 - 0.043ms returns 0x00
T7474 000:360.450 JLINK_ReadReg(R0)
T7474 000:360.480 - 0.029ms returns 0x00000000
T7474 000:360.961 JLINK_HasError()
T7474 000:360.988 JLINK_WriteReg(R0, 0x00000400)
T7474 000:361.008 - 0.020ms returns 0
T7474 000:361.029 JLINK_WriteReg(R1, 0x00000400)
T7474 000:361.048 - 0.019ms returns 0
T7474 000:361.068 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:361.087 - 0.019ms returns 0
T7474 000:361.107 JLINK_WriteReg(R3, 0x00000000)
T7474 000:361.132 - 0.025ms returns 0
T7474 000:361.152 JLINK_WriteReg(R4, 0x00000000)
T7474 000:361.171 - 0.018ms returns 0
T7474 000:361.191 JLINK_WriteReg(R5, 0x00000000)
T7474 000:361.210 - 0.019ms returns 0
T7474 000:361.230 JLINK_WriteReg(R6, 0x00000000)
T7474 000:361.249 - 0.019ms returns 0
T7474 000:361.270 JLINK_WriteReg(R7, 0x00000000)
T7474 000:361.288 - 0.018ms returns 0
T7474 000:361.311 JLINK_WriteReg(R8, 0x00000000)
T7474 000:361.331 - 0.020ms returns 0
T7474 000:361.351 JLINK_WriteReg(R9, 0x20200290)
T7474 000:361.370 - 0.018ms returns 0
T7474 000:361.390 JLINK_WriteReg(R10, 0x00000000)
T7474 000:361.409 - 0.018ms returns 0
T7474 000:361.429 JLINK_WriteReg(R11, 0x00000000)
T7474 000:361.448 - 0.018ms returns 0
T7474 000:361.472 JLINK_WriteReg(R12, 0x00000000)
T7474 000:361.490 - 0.018ms returns 0
T7474 000:361.510 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:361.529 - 0.019ms returns 0
T7474 000:361.549 JLINK_WriteReg(R14, 0x20200001)
T7474 000:361.568 - 0.019ms returns 0
T7474 000:361.588 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:361.607 - 0.019ms returns 0
T7474 000:361.627 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:361.646 - 0.019ms returns 0
T7474 000:361.666 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:361.685 - 0.018ms returns 0
T7474 000:361.705 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:361.724 - 0.018ms returns 0
T7474 000:361.744 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:361.762 - 0.018ms returns 0
T7474 000:361.782 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:361.802 - 0.019ms returns 0x00000004
T7474 000:361.821 JLINK_Go()
T7474 000:361.843   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:365.217 - 3.395ms
T7474 000:365.244 JLINK_IsHalted()
T7474 000:368.156   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:368.885 - 3.640ms returns TRUE
T7474 000:368.911 JLINK_ReadReg(R15 (PC))
T7474 000:368.931 - 0.020ms returns 0x20200000
T7474 000:368.951 JLINK_ClrBPEx(BPHandle = 0x00000004)
T7474 000:368.970 - 0.019ms returns 0x00
T7474 000:368.990 JLINK_ReadReg(R0)
T7474 000:369.009 - 0.018ms returns 0x00000001
T7474 000:369.029 JLINK_HasError()
T7474 000:369.049 JLINK_WriteReg(R0, 0x00000400)
T7474 000:369.068 - 0.019ms returns 0
T7474 000:369.088 JLINK_WriteReg(R1, 0x00000400)
T7474 000:369.107 - 0.019ms returns 0
T7474 000:369.127 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:369.145 - 0.018ms returns 0
T7474 000:369.165 JLINK_WriteReg(R3, 0x00000000)
T7474 000:369.184 - 0.018ms returns 0
T7474 000:369.204 JLINK_WriteReg(R4, 0x00000000)
T7474 000:369.227 - 0.023ms returns 0
T7474 000:369.247 JLINK_WriteReg(R5, 0x00000000)
T7474 000:369.266 - 0.018ms returns 0
T7474 000:369.285 JLINK_WriteReg(R6, 0x00000000)
T7474 000:369.304 - 0.018ms returns 0
T7474 000:369.324 JLINK_WriteReg(R7, 0x00000000)
T7474 000:369.343 - 0.018ms returns 0
T7474 000:369.363 JLINK_WriteReg(R8, 0x00000000)
T7474 000:369.382 - 0.018ms returns 0
T7474 000:369.401 JLINK_WriteReg(R9, 0x20200290)
T7474 000:369.420 - 0.018ms returns 0
T7474 000:369.440 JLINK_WriteReg(R10, 0x00000000)
T7474 000:369.459 - 0.018ms returns 0
T7474 000:369.478 JLINK_WriteReg(R11, 0x00000000)
T7474 000:369.497 - 0.018ms returns 0
T7474 000:369.517 JLINK_WriteReg(R12, 0x00000000)
T7474 000:369.536 - 0.018ms returns 0
T7474 000:369.555 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:369.574 - 0.018ms returns 0
T7474 000:369.594 JLINK_WriteReg(R14, 0x20200001)
T7474 000:369.613 - 0.018ms returns 0
T7474 000:369.633 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:369.651 - 0.018ms returns 0
T7474 000:369.671 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:369.690 - 0.018ms returns 0
T7474 000:369.710 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:369.728 - 0.018ms returns 0
T7474 000:369.748 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:369.767 - 0.018ms returns 0
T7474 000:369.787 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:369.805 - 0.018ms returns 0
T7474 000:369.825 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:369.844 - 0.019ms returns 0x00000005
T7474 000:369.864 JLINK_Go()
T7474 000:369.886   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:373.476 - 3.610ms
T7474 000:373.522 JLINK_IsHalted()
T7474 000:374.246 - 0.724ms returns FALSE
T7474 000:374.272 JLINK_HasError()
T7474 000:378.745 JLINK_IsHalted()
T7474 000:381.495   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:382.270 - 3.524ms returns TRUE
T7474 000:382.336 JLINK_ReadReg(R15 (PC))
T7474 000:382.384 - 0.048ms returns 0x20200000
T7474 000:382.435 JLINK_ClrBPEx(BPHandle = 0x00000005)
T7474 000:382.477 - 0.042ms returns 0x00
T7474 000:382.513 JLINK_ReadReg(R0)
T7474 000:382.533 - 0.019ms returns 0x00000000
T7474 000:382.990 JLINK_HasError()
T7474 000:383.016 JLINK_WriteReg(R0, 0x00000800)
T7474 000:383.036 - 0.019ms returns 0
T7474 000:383.055 JLINK_WriteReg(R1, 0x00000400)
T7474 000:383.074 - 0.019ms returns 0
T7474 000:383.094 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:383.113 - 0.018ms returns 0
T7474 000:383.133 JLINK_WriteReg(R3, 0x00000000)
T7474 000:383.151 - 0.018ms returns 0
T7474 000:383.171 JLINK_WriteReg(R4, 0x00000000)
T7474 000:383.190 - 0.018ms returns 0
T7474 000:383.210 JLINK_WriteReg(R5, 0x00000000)
T7474 000:383.228 - 0.018ms returns 0
T7474 000:383.248 JLINK_WriteReg(R6, 0x00000000)
T7474 000:383.267 - 0.018ms returns 0
T7474 000:383.286 JLINK_WriteReg(R7, 0x00000000)
T7474 000:383.305 - 0.018ms returns 0
T7474 000:383.325 JLINK_WriteReg(R8, 0x00000000)
T7474 000:383.344 - 0.018ms returns 0
T7474 000:383.363 JLINK_WriteReg(R9, 0x20200290)
T7474 000:383.382 - 0.018ms returns 0
T7474 000:383.402 JLINK_WriteReg(R10, 0x00000000)
T7474 000:383.421 - 0.018ms returns 0
T7474 000:383.440 JLINK_WriteReg(R11, 0x00000000)
T7474 000:383.459 - 0.018ms returns 0
T7474 000:383.479 JLINK_WriteReg(R12, 0x00000000)
T7474 000:383.497 - 0.018ms returns 0
T7474 000:383.517 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:383.536 - 0.018ms returns 0
T7474 000:383.556 JLINK_WriteReg(R14, 0x20200001)
T7474 000:383.575 - 0.018ms returns 0
T7474 000:383.594 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:383.613 - 0.018ms returns 0
T7474 000:383.633 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:383.652 - 0.018ms returns 0
T7474 000:383.671 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:383.690 - 0.018ms returns 0
T7474 000:383.710 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:383.728 - 0.018ms returns 0
T7474 000:383.748 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:383.767 - 0.018ms returns 0
T7474 000:383.787 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:383.807 - 0.019ms returns 0x00000006
T7474 000:383.826 JLINK_Go()
T7474 000:383.848   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:387.335 - 3.508ms
T7474 000:387.362 JLINK_IsHalted()
T7474 000:390.246   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:391.030 - 3.667ms returns TRUE
T7474 000:391.090 JLINK_ReadReg(R15 (PC))
T7474 000:391.135 - 0.044ms returns 0x20200000
T7474 000:391.180 JLINK_ClrBPEx(BPHandle = 0x00000006)
T7474 000:391.223 - 0.042ms returns 0x00
T7474 000:391.267 JLINK_ReadReg(R0)
T7474 000:391.310 - 0.042ms returns 0x00000001
T7474 000:391.354 JLINK_HasError()
T7474 000:391.399 JLINK_WriteReg(R0, 0x00000800)
T7474 000:391.442 - 0.042ms returns 0
T7474 000:391.486 JLINK_WriteReg(R1, 0x00000400)
T7474 000:391.529 - 0.042ms returns 0
T7474 000:391.548 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:391.567 - 0.018ms returns 0
T7474 000:391.587 JLINK_WriteReg(R3, 0x00000000)
T7474 000:391.606 - 0.018ms returns 0
T7474 000:391.626 JLINK_WriteReg(R4, 0x00000000)
T7474 000:391.644 - 0.018ms returns 0
T7474 000:391.664 JLINK_WriteReg(R5, 0x00000000)
T7474 000:391.683 - 0.018ms returns 0
T7474 000:391.703 JLINK_WriteReg(R6, 0x00000000)
T7474 000:391.721 - 0.018ms returns 0
T7474 000:391.741 JLINK_WriteReg(R7, 0x00000000)
T7474 000:391.760 - 0.018ms returns 0
T7474 000:391.780 JLINK_WriteReg(R8, 0x00000000)
T7474 000:391.798 - 0.018ms returns 0
T7474 000:391.818 JLINK_WriteReg(R9, 0x20200290)
T7474 000:391.837 - 0.018ms returns 0
T7474 000:391.856 JLINK_WriteReg(R10, 0x00000000)
T7474 000:391.875 - 0.018ms returns 0
T7474 000:391.895 JLINK_WriteReg(R11, 0x00000000)
T7474 000:391.914 - 0.018ms returns 0
T7474 000:391.933 JLINK_WriteReg(R12, 0x00000000)
T7474 000:391.952 - 0.018ms returns 0
T7474 000:391.972 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:391.991 - 0.019ms returns 0
T7474 000:392.010 JLINK_WriteReg(R14, 0x20200001)
T7474 000:392.029 - 0.018ms returns 0
T7474 000:392.049 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:392.071 - 0.022ms returns 0
T7474 000:392.091 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:392.109 - 0.018ms returns 0
T7474 000:392.129 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:392.148 - 0.018ms returns 0
T7474 000:392.168 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:392.186 - 0.018ms returns 0
T7474 000:392.206 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:392.225 - 0.018ms returns 0
T7474 000:392.245 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:392.264 - 0.019ms returns 0x00000007
T7474 000:392.283 JLINK_Go()
T7474 000:392.304   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:395.710 - 3.426ms
T7474 000:395.737 JLINK_IsHalted()
T7474 000:396.373 - 0.635ms returns FALSE
T7474 000:396.399 JLINK_HasError()
T7474 000:399.044 JLINK_IsHalted()
T7474 000:402.014   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:402.768 - 3.724ms returns TRUE
T7474 000:402.795 JLINK_ReadReg(R15 (PC))
T7474 000:402.816 - 0.020ms returns 0x20200000
T7474 000:402.836 JLINK_ClrBPEx(BPHandle = 0x00000007)
T7474 000:402.855 - 0.019ms returns 0x00
T7474 000:402.875 JLINK_ReadReg(R0)
T7474 000:402.895 - 0.019ms returns 0x00000000
T7474 000:403.351 JLINK_HasError()
T7474 000:403.377 JLINK_WriteReg(R0, 0x00000C00)
T7474 000:403.397 - 0.020ms returns 0
T7474 000:403.417 JLINK_WriteReg(R1, 0x00000400)
T7474 000:403.436 - 0.019ms returns 0
T7474 000:403.456 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:403.475 - 0.019ms returns 0
T7474 000:403.495 JLINK_WriteReg(R3, 0x00000000)
T7474 000:403.514 - 0.019ms returns 0
T7474 000:403.534 JLINK_WriteReg(R4, 0x00000000)
T7474 000:403.554 - 0.020ms returns 0
T7474 000:403.578 JLINK_WriteReg(R5, 0x00000000)
T7474 000:403.597 - 0.019ms returns 0
T7474 000:403.617 JLINK_WriteReg(R6, 0x00000000)
T7474 000:403.635 - 0.018ms returns 0
T7474 000:403.655 JLINK_WriteReg(R7, 0x00000000)
T7474 000:403.674 - 0.018ms returns 0
T7474 000:403.694 JLINK_WriteReg(R8, 0x00000000)
T7474 000:403.713 - 0.019ms returns 0
T7474 000:403.733 JLINK_WriteReg(R9, 0x20200290)
T7474 000:403.752 - 0.018ms returns 0
T7474 000:403.772 JLINK_WriteReg(R10, 0x00000000)
T7474 000:403.791 - 0.019ms returns 0
T7474 000:403.811 JLINK_WriteReg(R11, 0x00000000)
T7474 000:403.830 - 0.018ms returns 0
T7474 000:403.850 JLINK_WriteReg(R12, 0x00000000)
T7474 000:403.869 - 0.019ms returns 0
T7474 000:403.889 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:403.908 - 0.019ms returns 0
T7474 000:403.928 JLINK_WriteReg(R14, 0x20200001)
T7474 000:403.946 - 0.018ms returns 0
T7474 000:403.966 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:403.985 - 0.019ms returns 0
T7474 000:404.005 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:404.025 - 0.019ms returns 0
T7474 000:404.045 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:404.064 - 0.018ms returns 0
T7474 000:404.084 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:404.102 - 0.018ms returns 0
T7474 000:404.122 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:404.141 - 0.018ms returns 0
T7474 000:404.162 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:404.181 - 0.019ms returns 0x00000008
T7474 000:404.201 JLINK_Go()
T7474 000:404.223   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:411.226 - 7.024ms
T7474 000:411.292 JLINK_IsHalted()
T7474 000:414.123   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:414.893 - 3.600ms returns TRUE
T7474 000:414.923 JLINK_ReadReg(R15 (PC))
T7474 000:414.943 - 0.020ms returns 0x20200000
T7474 000:414.964 JLINK_ClrBPEx(BPHandle = 0x00000008)
T7474 000:414.983 - 0.019ms returns 0x00
T7474 000:415.005 JLINK_ReadReg(R0)
T7474 000:415.024 - 0.018ms returns 0x00000001
T7474 000:415.045 JLINK_HasError()
T7474 000:415.067 JLINK_WriteReg(R0, 0x00000C00)
T7474 000:415.086 - 0.019ms returns 0
T7474 000:415.111 JLINK_WriteReg(R1, 0x00000400)
T7474 000:415.130 - 0.018ms returns 0
T7474 000:415.151 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:415.170 - 0.018ms returns 0
T7474 000:415.191 JLINK_WriteReg(R3, 0x00000000)
T7474 000:415.209 - 0.018ms returns 0
T7474 000:415.231 JLINK_WriteReg(R4, 0x00000000)
T7474 000:415.292 - 0.061ms returns 0
T7474 000:415.313 JLINK_WriteReg(R5, 0x00000000)
T7474 000:415.332 - 0.018ms returns 0
T7474 000:415.353 JLINK_WriteReg(R6, 0x00000000)
T7474 000:415.372 - 0.018ms returns 0
T7474 000:415.393 JLINK_WriteReg(R7, 0x00000000)
T7474 000:415.412 - 0.018ms returns 0
T7474 000:415.433 JLINK_WriteReg(R8, 0x00000000)
T7474 000:415.452 - 0.018ms returns 0
T7474 000:415.473 JLINK_WriteReg(R9, 0x20200290)
T7474 000:415.491 - 0.018ms returns 0
T7474 000:415.513 JLINK_WriteReg(R10, 0x00000000)
T7474 000:415.531 - 0.018ms returns 0
T7474 000:415.552 JLINK_WriteReg(R11, 0x00000000)
T7474 000:415.571 - 0.018ms returns 0
T7474 000:415.596 JLINK_WriteReg(R12, 0x00000000)
T7474 000:415.615 - 0.018ms returns 0
T7474 000:415.636 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:415.655 - 0.018ms returns 0
T7474 000:415.676 JLINK_WriteReg(R14, 0x20200001)
T7474 000:415.695 - 0.018ms returns 0
T7474 000:415.717 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:415.735 - 0.018ms returns 0
T7474 000:415.757 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:415.775 - 0.018ms returns 0
T7474 000:415.797 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:415.815 - 0.018ms returns 0
T7474 000:415.837 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:415.856 - 0.019ms returns 0
T7474 000:415.877 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:415.896 - 0.019ms returns 0
T7474 000:415.918 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:415.937 - 0.019ms returns 0x00000009
T7474 000:415.958 JLINK_Go()
T7474 000:415.979   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:419.474 - 3.515ms
T7474 000:419.503 JLINK_IsHalted()
T7474 000:420.253 - 0.749ms returns FALSE
T7474 000:420.283 JLINK_HasError()
T7474 000:423.298 JLINK_IsHalted()
T7474 000:425.985   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:426.760 - 3.461ms returns TRUE
T7474 000:426.787 JLINK_ReadReg(R15 (PC))
T7474 000:426.807 - 0.019ms returns 0x20200000
T7474 000:426.827 JLINK_ClrBPEx(BPHandle = 0x00000009)
T7474 000:426.846 - 0.018ms returns 0x00
T7474 000:426.865 JLINK_ReadReg(R0)
T7474 000:426.884 - 0.018ms returns 0x00000000
T7474 000:427.301 JLINK_HasError()
T7474 000:427.326 JLINK_WriteReg(R0, 0x00001000)
T7474 000:427.345 - 0.019ms returns 0
T7474 000:427.365 JLINK_WriteReg(R1, 0x00000400)
T7474 000:427.384 - 0.018ms returns 0
T7474 000:427.403 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:427.422 - 0.018ms returns 0
T7474 000:427.441 JLINK_WriteReg(R3, 0x00000000)
T7474 000:427.460 - 0.018ms returns 0
T7474 000:427.480 JLINK_WriteReg(R4, 0x00000000)
T7474 000:427.498 - 0.018ms returns 0
T7474 000:427.518 JLINK_WriteReg(R5, 0x00000000)
T7474 000:427.537 - 0.018ms returns 0
T7474 000:427.556 JLINK_WriteReg(R6, 0x00000000)
T7474 000:427.575 - 0.018ms returns 0
T7474 000:427.594 JLINK_WriteReg(R7, 0x00000000)
T7474 000:427.613 - 0.018ms returns 0
T7474 000:427.633 JLINK_WriteReg(R8, 0x00000000)
T7474 000:427.651 - 0.018ms returns 0
T7474 000:427.671 JLINK_WriteReg(R9, 0x20200290)
T7474 000:427.689 - 0.018ms returns 0
T7474 000:427.709 JLINK_WriteReg(R10, 0x00000000)
T7474 000:427.728 - 0.018ms returns 0
T7474 000:427.747 JLINK_WriteReg(R11, 0x00000000)
T7474 000:427.766 - 0.018ms returns 0
T7474 000:427.785 JLINK_WriteReg(R12, 0x00000000)
T7474 000:427.804 - 0.018ms returns 0
T7474 000:427.823 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:427.842 - 0.018ms returns 0
T7474 000:427.862 JLINK_WriteReg(R14, 0x20200001)
T7474 000:427.880 - 0.018ms returns 0
T7474 000:427.900 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:427.919 - 0.018ms returns 0
T7474 000:427.938 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:427.957 - 0.018ms returns 0
T7474 000:427.977 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:427.995 - 0.018ms returns 0
T7474 000:428.015 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:428.033 - 0.018ms returns 0
T7474 000:428.053 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:428.072 - 0.018ms returns 0
T7474 000:428.092 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:428.111 - 0.019ms returns 0x0000000A
T7474 000:428.154 JLINK_Go()
T7474 000:428.176   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:431.722 - 3.568ms
T7474 000:431.749 JLINK_IsHalted()
T7474 000:434.483   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:435.279 - 3.529ms returns TRUE
T7474 000:435.337 JLINK_ReadReg(R15 (PC))
T7474 000:435.381 - 0.044ms returns 0x20200000
T7474 000:435.426 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T7474 000:435.468 - 0.041ms returns 0x00
T7474 000:435.512 JLINK_ReadReg(R0)
T7474 000:435.553 - 0.041ms returns 0x00000001
T7474 000:435.598 JLINK_HasError()
T7474 000:435.624 JLINK_WriteReg(R0, 0x00001000)
T7474 000:435.643 - 0.018ms returns 0
T7474 000:435.663 JLINK_WriteReg(R1, 0x00000400)
T7474 000:435.681 - 0.018ms returns 0
T7474 000:435.701 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:435.719 - 0.018ms returns 0
T7474 000:435.739 JLINK_WriteReg(R3, 0x00000000)
T7474 000:435.757 - 0.018ms returns 0
T7474 000:435.777 JLINK_WriteReg(R4, 0x00000000)
T7474 000:435.795 - 0.018ms returns 0
T7474 000:435.815 JLINK_WriteReg(R5, 0x00000000)
T7474 000:435.833 - 0.018ms returns 0
T7474 000:435.853 JLINK_WriteReg(R6, 0x00000000)
T7474 000:435.871 - 0.018ms returns 0
T7474 000:435.891 JLINK_WriteReg(R7, 0x00000000)
T7474 000:435.909 - 0.018ms returns 0
T7474 000:435.929 JLINK_WriteReg(R8, 0x00000000)
T7474 000:435.947 - 0.018ms returns 0
T7474 000:435.967 JLINK_WriteReg(R9, 0x20200290)
T7474 000:435.985 - 0.018ms returns 0
T7474 000:436.005 JLINK_WriteReg(R10, 0x00000000)
T7474 000:436.023 - 0.018ms returns 0
T7474 000:436.043 JLINK_WriteReg(R11, 0x00000000)
T7474 000:436.061 - 0.018ms returns 0
T7474 000:436.081 JLINK_WriteReg(R12, 0x00000000)
T7474 000:436.099 - 0.018ms returns 0
T7474 000:436.119 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:436.137 - 0.018ms returns 0
T7474 000:436.157 JLINK_WriteReg(R14, 0x20200001)
T7474 000:436.175 - 0.018ms returns 0
T7474 000:436.195 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:436.213 - 0.018ms returns 0
T7474 000:436.233 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:436.252 - 0.018ms returns 0
T7474 000:436.271 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:436.290 - 0.018ms returns 0
T7474 000:436.309 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:436.328 - 0.018ms returns 0
T7474 000:436.347 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:436.366 - 0.018ms returns 0
T7474 000:436.385 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:436.404 - 0.018ms returns 0x0000000B
T7474 000:436.424 JLINK_Go()
T7474 000:436.444   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:439.842 - 3.418ms
T7474 000:439.869 JLINK_IsHalted()
T7474 000:440.627 - 0.757ms returns FALSE
T7474 000:440.654 JLINK_HasError()
T7474 000:445.034 JLINK_IsHalted()
T7474 000:447.861   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:448.715 - 3.680ms returns TRUE
T7474 000:448.744 JLINK_ReadReg(R15 (PC))
T7474 000:448.764 - 0.020ms returns 0x20200000
T7474 000:448.785 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T7474 000:448.805 - 0.019ms returns 0x00
T7474 000:448.826 JLINK_ReadReg(R0)
T7474 000:448.845 - 0.018ms returns 0x00000000
T7474 000:449.285 JLINK_HasError()
T7474 000:449.311 JLINK_WriteReg(R0, 0x00001400)
T7474 000:449.330 - 0.019ms returns 0
T7474 000:449.350 JLINK_WriteReg(R1, 0x00000400)
T7474 000:449.369 - 0.018ms returns 0
T7474 000:449.389 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:449.408 - 0.019ms returns 0
T7474 000:449.427 JLINK_WriteReg(R3, 0x00000000)
T7474 000:449.446 - 0.018ms returns 0
T7474 000:449.466 JLINK_WriteReg(R4, 0x00000000)
T7474 000:449.484 - 0.018ms returns 0
T7474 000:449.504 JLINK_WriteReg(R5, 0x00000000)
T7474 000:449.523 - 0.018ms returns 0
T7474 000:449.542 JLINK_WriteReg(R6, 0x00000000)
T7474 000:449.561 - 0.018ms returns 0
T7474 000:449.581 JLINK_WriteReg(R7, 0x00000000)
T7474 000:449.599 - 0.018ms returns 0
T7474 000:449.619 JLINK_WriteReg(R8, 0x00000000)
T7474 000:449.642 - 0.022ms returns 0
T7474 000:449.661 JLINK_WriteReg(R9, 0x20200290)
T7474 000:449.680 - 0.018ms returns 0
T7474 000:449.700 JLINK_WriteReg(R10, 0x00000000)
T7474 000:449.745 - 0.045ms returns 0
T7474 000:449.764 JLINK_WriteReg(R11, 0x00000000)
T7474 000:449.783 - 0.019ms returns 0
T7474 000:449.803 JLINK_WriteReg(R12, 0x00000000)
T7474 000:449.822 - 0.018ms returns 0
T7474 000:449.842 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:449.861 - 0.018ms returns 0
T7474 000:449.880 JLINK_WriteReg(R14, 0x20200001)
T7474 000:449.899 - 0.018ms returns 0
T7474 000:449.919 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:449.937 - 0.018ms returns 0
T7474 000:449.957 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:449.976 - 0.018ms returns 0
T7474 000:449.996 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:450.014 - 0.018ms returns 0
T7474 000:450.034 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:450.053 - 0.018ms returns 0
T7474 000:450.073 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:450.091 - 0.018ms returns 0
T7474 000:450.111 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:450.130 - 0.019ms returns 0x0000000C
T7474 000:450.150 JLINK_Go()
T7474 000:450.172   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:453.755 - 3.604ms
T7474 000:453.785 JLINK_IsHalted()
T7474 000:456.506   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:457.284 - 3.498ms returns TRUE
T7474 000:457.342 JLINK_ReadReg(R15 (PC))
T7474 000:457.386 - 0.044ms returns 0x20200000
T7474 000:457.431 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T7474 000:457.473 - 0.042ms returns 0x00
T7474 000:457.517 JLINK_ReadReg(R0)
T7474 000:457.559 - 0.041ms returns 0x00000001
T7474 000:457.613 JLINK_HasError()
T7474 000:457.651 JLINK_WriteReg(R0, 0x00001400)
T7474 000:457.670 - 0.019ms returns 0
T7474 000:457.690 JLINK_WriteReg(R1, 0x00000400)
T7474 000:457.708 - 0.018ms returns 0
T7474 000:457.728 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:457.746 - 0.018ms returns 0
T7474 000:457.766 JLINK_WriteReg(R3, 0x00000000)
T7474 000:457.784 - 0.018ms returns 0
T7474 000:457.803 JLINK_WriteReg(R4, 0x00000000)
T7474 000:457.822 - 0.018ms returns 0
T7474 000:457.841 JLINK_WriteReg(R5, 0x00000000)
T7474 000:457.860 - 0.018ms returns 0
T7474 000:457.879 JLINK_WriteReg(R6, 0x00000000)
T7474 000:457.898 - 0.018ms returns 0
T7474 000:457.917 JLINK_WriteReg(R7, 0x00000000)
T7474 000:457.935 - 0.018ms returns 0
T7474 000:457.955 JLINK_WriteReg(R8, 0x00000000)
T7474 000:457.973 - 0.018ms returns 0
T7474 000:457.993 JLINK_WriteReg(R9, 0x20200290)
T7474 000:458.011 - 0.018ms returns 0
T7474 000:458.030 JLINK_WriteReg(R10, 0x00000000)
T7474 000:458.049 - 0.018ms returns 0
T7474 000:458.068 JLINK_WriteReg(R11, 0x00000000)
T7474 000:458.086 - 0.018ms returns 0
T7474 000:458.106 JLINK_WriteReg(R12, 0x00000000)
T7474 000:458.124 - 0.018ms returns 0
T7474 000:458.144 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:458.162 - 0.018ms returns 0
T7474 000:458.181 JLINK_WriteReg(R14, 0x20200001)
T7474 000:458.200 - 0.018ms returns 0
T7474 000:458.219 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:458.238 - 0.018ms returns 0
T7474 000:458.257 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:458.275 - 0.018ms returns 0
T7474 000:458.295 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:458.313 - 0.018ms returns 0
T7474 000:458.333 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:458.351 - 0.018ms returns 0
T7474 000:458.371 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:458.389 - 0.018ms returns 0
T7474 000:458.409 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:458.427 - 0.018ms returns 0x0000000D
T7474 000:458.447 JLINK_Go()
T7474 000:458.468   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:461.842 - 3.394ms
T7474 000:461.868 JLINK_IsHalted()
T7474 000:462.629 - 0.760ms returns FALSE
T7474 000:462.655 JLINK_HasError()
T7474 000:465.970 JLINK_IsHalted()
T7474 000:468.783   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:469.545 - 3.574ms returns TRUE
T7474 000:469.618 JLINK_ReadReg(R15 (PC))
T7474 000:469.673 - 0.056ms returns 0x20200000
T7474 000:469.693 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T7474 000:469.712 - 0.019ms returns 0x00
T7474 000:469.732 JLINK_ReadReg(R0)
T7474 000:469.751 - 0.018ms returns 0x00000000
T7474 000:470.593 JLINK_HasError()
T7474 000:470.620 JLINK_WriteReg(R0, 0x00001800)
T7474 000:470.640 - 0.020ms returns 0
T7474 000:470.660 JLINK_WriteReg(R1, 0x00000400)
T7474 000:470.679 - 0.018ms returns 0
T7474 000:470.698 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:470.717 - 0.018ms returns 0
T7474 000:470.737 JLINK_WriteReg(R3, 0x00000000)
T7474 000:470.755 - 0.018ms returns 0
T7474 000:470.775 JLINK_WriteReg(R4, 0x00000000)
T7474 000:470.794 - 0.018ms returns 0
T7474 000:470.813 JLINK_WriteReg(R5, 0x00000000)
T7474 000:470.832 - 0.018ms returns 0
T7474 000:470.851 JLINK_WriteReg(R6, 0x00000000)
T7474 000:470.870 - 0.018ms returns 0
T7474 000:470.889 JLINK_WriteReg(R7, 0x00000000)
T7474 000:470.908 - 0.018ms returns 0
T7474 000:470.927 JLINK_WriteReg(R8, 0x00000000)
T7474 000:470.946 - 0.018ms returns 0
T7474 000:470.966 JLINK_WriteReg(R9, 0x20200290)
T7474 000:470.984 - 0.018ms returns 0
T7474 000:471.004 JLINK_WriteReg(R10, 0x00000000)
T7474 000:471.023 - 0.018ms returns 0
T7474 000:471.042 JLINK_WriteReg(R11, 0x00000000)
T7474 000:471.061 - 0.018ms returns 0
T7474 000:471.081 JLINK_WriteReg(R12, 0x00000000)
T7474 000:471.099 - 0.018ms returns 0
T7474 000:471.119 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:471.138 - 0.018ms returns 0
T7474 000:471.157 JLINK_WriteReg(R14, 0x20200001)
T7474 000:471.176 - 0.018ms returns 0
T7474 000:471.196 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:471.214 - 0.018ms returns 0
T7474 000:471.234 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:471.253 - 0.018ms returns 0
T7474 000:471.277 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:471.296 - 0.018ms returns 0
T7474 000:471.316 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:471.334 - 0.018ms returns 0
T7474 000:471.354 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:471.372 - 0.018ms returns 0
T7474 000:471.392 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:471.411 - 0.019ms returns 0x0000000E
T7474 000:471.431 JLINK_Go()
T7474 000:471.452   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:474.921 - 3.489ms
T7474 000:474.950 JLINK_IsHalted()
T7474 000:477.727   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:478.509 - 3.558ms returns TRUE
T7474 000:478.538 JLINK_ReadReg(R15 (PC))
T7474 000:478.558 - 0.020ms returns 0x20200000
T7474 000:478.580 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T7474 000:478.599 - 0.019ms returns 0x00
T7474 000:478.620 JLINK_ReadReg(R0)
T7474 000:478.639 - 0.018ms returns 0x00000001
T7474 000:478.661 JLINK_HasError()
T7474 000:478.682 JLINK_WriteReg(R0, 0x00001800)
T7474 000:478.705 - 0.023ms returns 0
T7474 000:478.726 JLINK_WriteReg(R1, 0x00000400)
T7474 000:478.745 - 0.018ms returns 0
T7474 000:478.766 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:478.785 - 0.018ms returns 0
T7474 000:478.806 JLINK_WriteReg(R3, 0x00000000)
T7474 000:478.824 - 0.018ms returns 0
T7474 000:478.845 JLINK_WriteReg(R4, 0x00000000)
T7474 000:478.864 - 0.018ms returns 0
T7474 000:478.885 JLINK_WriteReg(R5, 0x00000000)
T7474 000:478.904 - 0.018ms returns 0
T7474 000:478.925 JLINK_WriteReg(R6, 0x00000000)
T7474 000:478.943 - 0.018ms returns 0
T7474 000:478.965 JLINK_WriteReg(R7, 0x00000000)
T7474 000:478.984 - 0.018ms returns 0
T7474 000:479.005 JLINK_WriteReg(R8, 0x00000000)
T7474 000:479.024 - 0.018ms returns 0
T7474 000:479.045 JLINK_WriteReg(R9, 0x20200290)
T7474 000:479.064 - 0.019ms returns 0
T7474 000:479.085 JLINK_WriteReg(R10, 0x00000000)
T7474 000:479.104 - 0.018ms returns 0
T7474 000:479.126 JLINK_WriteReg(R11, 0x00000000)
T7474 000:479.145 - 0.018ms returns 0
T7474 000:479.167 JLINK_WriteReg(R12, 0x00000000)
T7474 000:479.185 - 0.018ms returns 0
T7474 000:479.207 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:479.225 - 0.018ms returns 0
T7474 000:479.246 JLINK_WriteReg(R14, 0x20200001)
T7474 000:479.265 - 0.018ms returns 0
T7474 000:479.286 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:479.305 - 0.018ms returns 0
T7474 000:479.326 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:479.345 - 0.018ms returns 0
T7474 000:479.366 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:479.414 - 0.047ms returns 0
T7474 000:479.435 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:479.454 - 0.018ms returns 0
T7474 000:479.475 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:479.494 - 0.018ms returns 0
T7474 000:479.515 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:479.534 - 0.019ms returns 0x0000000F
T7474 000:479.555 JLINK_Go()
T7474 000:479.577   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:482.973 - 3.417ms
T7474 000:483.003 JLINK_IsHalted()
T7474 000:483.750 - 0.746ms returns FALSE
T7474 000:483.779 JLINK_HasError()
T7474 000:486.389 JLINK_IsHalted()
T7474 000:489.231   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:490.008 - 3.619ms returns TRUE
T7474 000:490.035 JLINK_ReadReg(R15 (PC))
T7474 000:490.055 - 0.020ms returns 0x20200000
T7474 000:490.075 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T7474 000:490.094 - 0.018ms returns 0x00
T7474 000:490.114 JLINK_ReadReg(R0)
T7474 000:490.133 - 0.018ms returns 0x00000000
T7474 000:490.541 JLINK_HasError()
T7474 000:490.566 JLINK_WriteReg(R0, 0x00001C00)
T7474 000:490.585 - 0.019ms returns 0
T7474 000:490.605 JLINK_WriteReg(R1, 0x00000400)
T7474 000:490.623 - 0.018ms returns 0
T7474 000:490.643 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:490.662 - 0.018ms returns 0
T7474 000:490.682 JLINK_WriteReg(R3, 0x00000000)
T7474 000:490.700 - 0.018ms returns 0
T7474 000:490.720 JLINK_WriteReg(R4, 0x00000000)
T7474 000:490.739 - 0.018ms returns 0
T7474 000:490.758 JLINK_WriteReg(R5, 0x00000000)
T7474 000:490.777 - 0.018ms returns 0
T7474 000:490.797 JLINK_WriteReg(R6, 0x00000000)
T7474 000:490.815 - 0.018ms returns 0
T7474 000:490.835 JLINK_WriteReg(R7, 0x00000000)
T7474 000:490.853 - 0.018ms returns 0
T7474 000:490.873 JLINK_WriteReg(R8, 0x00000000)
T7474 000:490.892 - 0.018ms returns 0
T7474 000:490.911 JLINK_WriteReg(R9, 0x20200290)
T7474 000:490.930 - 0.018ms returns 0
T7474 000:490.950 JLINK_WriteReg(R10, 0x00000000)
T7474 000:490.968 - 0.018ms returns 0
T7474 000:490.988 JLINK_WriteReg(R11, 0x00000000)
T7474 000:491.006 - 0.018ms returns 0
T7474 000:491.026 JLINK_WriteReg(R12, 0x00000000)
T7474 000:491.045 - 0.018ms returns 0
T7474 000:491.064 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:491.083 - 0.018ms returns 0
T7474 000:491.103 JLINK_WriteReg(R14, 0x20200001)
T7474 000:491.121 - 0.018ms returns 0
T7474 000:491.141 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:491.159 - 0.018ms returns 0
T7474 000:491.179 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:491.198 - 0.018ms returns 0
T7474 000:491.217 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:491.236 - 0.018ms returns 0
T7474 000:491.255 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:491.274 - 0.018ms returns 0
T7474 000:491.294 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:491.312 - 0.018ms returns 0
T7474 000:491.332 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:491.351 - 0.019ms returns 0x00000010
T7474 000:491.371 JLINK_Go()
T7474 000:491.392   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:495.011 - 3.639ms
T7474 000:495.071 JLINK_IsHalted()
T7474 000:497.865   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:498.639 - 3.568ms returns TRUE
T7474 000:498.666 JLINK_ReadReg(R15 (PC))
T7474 000:498.686 - 0.019ms returns 0x20200000
T7474 000:498.706 JLINK_ClrBPEx(BPHandle = 0x00000010)
T7474 000:498.724 - 0.018ms returns 0x00
T7474 000:498.744 JLINK_ReadReg(R0)
T7474 000:498.763 - 0.018ms returns 0x00000001
T7474 000:498.783 JLINK_HasError()
T7474 000:498.803 JLINK_WriteReg(R0, 0x00001C00)
T7474 000:498.822 - 0.019ms returns 0
T7474 000:498.841 JLINK_WriteReg(R1, 0x00000400)
T7474 000:498.860 - 0.018ms returns 0
T7474 000:498.880 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:498.898 - 0.018ms returns 0
T7474 000:498.918 JLINK_WriteReg(R3, 0x00000000)
T7474 000:498.942 - 0.023ms returns 0
T7474 000:498.961 JLINK_WriteReg(R4, 0x00000000)
T7474 000:498.980 - 0.018ms returns 0
T7474 000:499.000 JLINK_WriteReg(R5, 0x00000000)
T7474 000:499.018 - 0.018ms returns 0
T7474 000:499.038 JLINK_WriteReg(R6, 0x00000000)
T7474 000:499.078 - 0.040ms returns 0
T7474 000:499.099 JLINK_WriteReg(R7, 0x00000000)
T7474 000:499.118 - 0.018ms returns 0
T7474 000:499.138 JLINK_WriteReg(R8, 0x00000000)
T7474 000:499.156 - 0.018ms returns 0
T7474 000:499.176 JLINK_WriteReg(R9, 0x20200290)
T7474 000:499.194 - 0.018ms returns 0
T7474 000:499.214 JLINK_WriteReg(R10, 0x00000000)
T7474 000:499.233 - 0.018ms returns 0
T7474 000:499.252 JLINK_WriteReg(R11, 0x00000000)
T7474 000:499.271 - 0.018ms returns 0
T7474 000:499.291 JLINK_WriteReg(R12, 0x00000000)
T7474 000:499.309 - 0.018ms returns 0
T7474 000:499.329 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:499.347 - 0.018ms returns 0
T7474 000:499.367 JLINK_WriteReg(R14, 0x20200001)
T7474 000:499.386 - 0.018ms returns 0
T7474 000:499.406 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:499.425 - 0.018ms returns 0
T7474 000:499.444 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:499.463 - 0.018ms returns 0
T7474 000:499.483 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:499.501 - 0.018ms returns 0
T7474 000:499.521 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:499.539 - 0.018ms returns 0
T7474 000:499.559 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:499.578 - 0.018ms returns 0
T7474 000:499.598 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:499.617 - 0.019ms returns 0x00000011
T7474 000:499.636 JLINK_Go()
T7474 000:499.657   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:503.219 - 3.582ms
T7474 000:503.245 JLINK_IsHalted()
T7474 000:504.010 - 0.764ms returns FALSE
T7474 000:504.036 JLINK_HasError()
T7474 000:506.950 JLINK_IsHalted()
T7474 000:509.847   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:510.513 - 3.562ms returns TRUE
T7474 000:510.576 JLINK_ReadReg(R15 (PC))
T7474 000:510.620 - 0.043ms returns 0x20200000
T7474 000:510.668 JLINK_ClrBPEx(BPHandle = 0x00000011)
T7474 000:510.709 - 0.042ms returns 0x00
T7474 000:510.756 JLINK_ReadReg(R0)
T7474 000:510.784 - 0.027ms returns 0x00000000
T7474 000:511.244 JLINK_HasError()
T7474 000:511.270 JLINK_WriteReg(R0, 0x00002000)
T7474 000:511.290 - 0.019ms returns 0
T7474 000:511.309 JLINK_WriteReg(R1, 0x00000400)
T7474 000:511.328 - 0.018ms returns 0
T7474 000:511.347 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:511.366 - 0.018ms returns 0
T7474 000:511.385 JLINK_WriteReg(R3, 0x00000000)
T7474 000:511.404 - 0.018ms returns 0
T7474 000:511.423 JLINK_WriteReg(R4, 0x00000000)
T7474 000:511.441 - 0.018ms returns 0
T7474 000:511.461 JLINK_WriteReg(R5, 0x00000000)
T7474 000:511.479 - 0.018ms returns 0
T7474 000:511.499 JLINK_WriteReg(R6, 0x00000000)
T7474 000:511.517 - 0.018ms returns 0
T7474 000:511.537 JLINK_WriteReg(R7, 0x00000000)
T7474 000:511.555 - 0.018ms returns 0
T7474 000:511.574 JLINK_WriteReg(R8, 0x00000000)
T7474 000:511.598 - 0.024ms returns 0
T7474 000:511.618 JLINK_WriteReg(R9, 0x20200290)
T7474 000:511.636 - 0.018ms returns 0
T7474 000:511.656 JLINK_WriteReg(R10, 0x00000000)
T7474 000:511.674 - 0.018ms returns 0
T7474 000:511.693 JLINK_WriteReg(R11, 0x00000000)
T7474 000:511.712 - 0.018ms returns 0
T7474 000:511.731 JLINK_WriteReg(R12, 0x00000000)
T7474 000:511.754 - 0.022ms returns 0
T7474 000:511.773 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:511.792 - 0.019ms returns 0
T7474 000:511.812 JLINK_WriteReg(R14, 0x20200001)
T7474 000:511.831 - 0.018ms returns 0
T7474 000:511.850 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:511.869 - 0.018ms returns 0
T7474 000:511.889 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:511.907 - 0.018ms returns 0
T7474 000:511.927 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:511.946 - 0.018ms returns 0
T7474 000:511.965 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:511.984 - 0.018ms returns 0
T7474 000:512.003 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:512.022 - 0.018ms returns 0
T7474 000:512.042 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:512.061 - 0.019ms returns 0x00000012
T7474 000:512.081 JLINK_Go()
T7474 000:512.102   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:515.732 - 3.650ms
T7474 000:515.769 JLINK_IsHalted()
T7474 000:518.583   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:519.390 - 3.621ms returns TRUE
T7474 000:519.417 JLINK_ReadReg(R15 (PC))
T7474 000:519.437 - 0.020ms returns 0x20200000
T7474 000:519.458 JLINK_ClrBPEx(BPHandle = 0x00000012)
T7474 000:519.477 - 0.018ms returns 0x00
T7474 000:519.498 JLINK_ReadReg(R0)
T7474 000:519.517 - 0.018ms returns 0x00000001
T7474 000:519.537 JLINK_HasError()
T7474 000:519.557 JLINK_WriteReg(R0, 0x00002000)
T7474 000:519.576 - 0.019ms returns 0
T7474 000:519.595 JLINK_WriteReg(R1, 0x00000400)
T7474 000:519.614 - 0.018ms returns 0
T7474 000:519.634 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:519.652 - 0.018ms returns 0
T7474 000:519.672 JLINK_WriteReg(R3, 0x00000000)
T7474 000:519.691 - 0.018ms returns 0
T7474 000:519.710 JLINK_WriteReg(R4, 0x00000000)
T7474 000:519.729 - 0.018ms returns 0
T7474 000:519.748 JLINK_WriteReg(R5, 0x00000000)
T7474 000:519.771 - 0.022ms returns 0
T7474 000:519.790 JLINK_WriteReg(R6, 0x00000000)
T7474 000:519.809 - 0.018ms returns 0
T7474 000:519.829 JLINK_WriteReg(R7, 0x00000000)
T7474 000:519.848 - 0.018ms returns 0
T7474 000:519.867 JLINK_WriteReg(R8, 0x00000000)
T7474 000:519.886 - 0.018ms returns 0
T7474 000:519.906 JLINK_WriteReg(R9, 0x20200290)
T7474 000:519.925 - 0.018ms returns 0
T7474 000:519.944 JLINK_WriteReg(R10, 0x00000000)
T7474 000:519.963 - 0.018ms returns 0
T7474 000:519.983 JLINK_WriteReg(R11, 0x00000000)
T7474 000:520.001 - 0.018ms returns 0
T7474 000:520.021 JLINK_WriteReg(R12, 0x00000000)
T7474 000:520.040 - 0.018ms returns 0
T7474 000:520.059 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:520.078 - 0.019ms returns 0
T7474 000:520.098 JLINK_WriteReg(R14, 0x20200001)
T7474 000:520.117 - 0.018ms returns 0
T7474 000:520.136 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:520.155 - 0.018ms returns 0
T7474 000:520.175 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:520.193 - 0.018ms returns 0
T7474 000:520.218 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:520.237 - 0.018ms returns 0
T7474 000:520.256 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:520.275 - 0.018ms returns 0
T7474 000:520.295 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:520.313 - 0.018ms returns 0
T7474 000:520.333 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:520.352 - 0.019ms returns 0x00000013
T7474 000:520.372 JLINK_Go()
T7474 000:520.393   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:523.828 - 3.455ms
T7474 000:523.855 JLINK_IsHalted()
T7474 000:524.500 - 0.644ms returns FALSE
T7474 000:524.525 JLINK_HasError()
T7474 000:527.156 JLINK_IsHalted()
T7474 000:529.982   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:530.792 - 3.635ms returns TRUE
T7474 000:530.849 JLINK_ReadReg(R15 (PC))
T7474 000:530.888 - 0.039ms returns 0x20200000
T7474 000:530.908 JLINK_ClrBPEx(BPHandle = 0x00000013)
T7474 000:530.927 - 0.018ms returns 0x00
T7474 000:530.946 JLINK_ReadReg(R0)
T7474 000:530.965 - 0.018ms returns 0x00000000
T7474 000:531.394 JLINK_HasError()
T7474 000:531.418 JLINK_WriteReg(R0, 0x00002400)
T7474 000:531.438 - 0.019ms returns 0
T7474 000:531.457 JLINK_WriteReg(R1, 0x00000400)
T7474 000:531.476 - 0.018ms returns 0
T7474 000:531.495 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:531.514 - 0.018ms returns 0
T7474 000:531.533 JLINK_WriteReg(R3, 0x00000000)
T7474 000:531.552 - 0.018ms returns 0
T7474 000:531.571 JLINK_WriteReg(R4, 0x00000000)
T7474 000:531.590 - 0.018ms returns 0
T7474 000:531.609 JLINK_WriteReg(R5, 0x00000000)
T7474 000:531.628 - 0.018ms returns 0
T7474 000:531.647 JLINK_WriteReg(R6, 0x00000000)
T7474 000:531.665 - 0.018ms returns 0
T7474 000:531.685 JLINK_WriteReg(R7, 0x00000000)
T7474 000:531.703 - 0.018ms returns 0
T7474 000:531.723 JLINK_WriteReg(R8, 0x00000000)
T7474 000:531.741 - 0.018ms returns 0
T7474 000:531.760 JLINK_WriteReg(R9, 0x20200290)
T7474 000:531.779 - 0.018ms returns 0
T7474 000:531.802 JLINK_WriteReg(R10, 0x00000000)
T7474 000:531.821 - 0.018ms returns 0
T7474 000:531.840 JLINK_WriteReg(R11, 0x00000000)
T7474 000:531.859 - 0.018ms returns 0
T7474 000:531.879 JLINK_WriteReg(R12, 0x00000000)
T7474 000:531.922 - 0.043ms returns 0
T7474 000:531.946 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:531.965 - 0.018ms returns 0
T7474 000:531.984 JLINK_WriteReg(R14, 0x20200001)
T7474 000:532.003 - 0.018ms returns 0
T7474 000:532.022 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:532.041 - 0.018ms returns 0
T7474 000:532.061 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:532.079 - 0.018ms returns 0
T7474 000:532.098 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:532.117 - 0.018ms returns 0
T7474 000:532.136 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:532.155 - 0.018ms returns 0
T7474 000:532.174 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:532.193 - 0.018ms returns 0
T7474 000:532.213 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:532.232 - 0.019ms returns 0x00000014
T7474 000:532.251 JLINK_Go()
T7474 000:532.273   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:535.733 - 3.481ms
T7474 000:535.794 JLINK_IsHalted()
T7474 000:538.501   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:539.267 - 3.472ms returns TRUE
T7474 000:539.296 JLINK_ReadReg(R15 (PC))
T7474 000:539.316 - 0.019ms returns 0x20200000
T7474 000:539.337 JLINK_ClrBPEx(BPHandle = 0x00000014)
T7474 000:539.355 - 0.018ms returns 0x00
T7474 000:539.376 JLINK_ReadReg(R0)
T7474 000:539.395 - 0.018ms returns 0x00000001
T7474 000:539.416 JLINK_HasError()
T7474 000:539.437 JLINK_WriteReg(R0, 0x00002400)
T7474 000:539.456 - 0.018ms returns 0
T7474 000:539.477 JLINK_WriteReg(R1, 0x00000400)
T7474 000:539.496 - 0.018ms returns 0
T7474 000:539.517 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:539.535 - 0.018ms returns 0
T7474 000:539.556 JLINK_WriteReg(R3, 0x00000000)
T7474 000:539.574 - 0.018ms returns 0
T7474 000:539.595 JLINK_WriteReg(R4, 0x00000000)
T7474 000:539.614 - 0.018ms returns 0
T7474 000:539.635 JLINK_WriteReg(R5, 0x00000000)
T7474 000:539.653 - 0.018ms returns 0
T7474 000:539.674 JLINK_WriteReg(R6, 0x00000000)
T7474 000:539.693 - 0.018ms returns 0
T7474 000:539.714 JLINK_WriteReg(R7, 0x00000000)
T7474 000:539.732 - 0.018ms returns 0
T7474 000:539.753 JLINK_WriteReg(R8, 0x00000000)
T7474 000:539.772 - 0.018ms returns 0
T7474 000:539.792 JLINK_WriteReg(R9, 0x20200290)
T7474 000:539.811 - 0.018ms returns 0
T7474 000:539.832 JLINK_WriteReg(R10, 0x00000000)
T7474 000:539.850 - 0.018ms returns 0
T7474 000:539.871 JLINK_WriteReg(R11, 0x00000000)
T7474 000:539.890 - 0.018ms returns 0
T7474 000:539.911 JLINK_WriteReg(R12, 0x00000000)
T7474 000:539.929 - 0.018ms returns 0
T7474 000:539.950 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:539.969 - 0.018ms returns 0
T7474 000:539.990 JLINK_WriteReg(R14, 0x20200001)
T7474 000:540.008 - 0.018ms returns 0
T7474 000:540.029 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:540.048 - 0.018ms returns 0
T7474 000:540.069 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:540.087 - 0.018ms returns 0
T7474 000:540.108 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:540.127 - 0.018ms returns 0
T7474 000:540.152 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:540.171 - 0.018ms returns 0
T7474 000:540.192 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:540.211 - 0.018ms returns 0
T7474 000:540.232 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:540.251 - 0.018ms returns 0x00000015
T7474 000:540.272 JLINK_Go()
T7474 000:540.293   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:543.852 - 3.580ms
T7474 000:543.913 JLINK_IsHalted()
T7474 000:544.631 - 0.717ms returns FALSE
T7474 000:544.659 JLINK_HasError()
T7474 000:546.597 JLINK_IsHalted()
T7474 000:549.473   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:550.146 - 3.549ms returns TRUE
T7474 000:550.172 JLINK_ReadReg(R15 (PC))
T7474 000:550.192 - 0.019ms returns 0x20200000
T7474 000:550.212 JLINK_ClrBPEx(BPHandle = 0x00000015)
T7474 000:550.231 - 0.018ms returns 0x00
T7474 000:550.251 JLINK_ReadReg(R0)
T7474 000:550.269 - 0.018ms returns 0x00000000
T7474 000:550.666 JLINK_HasError()
T7474 000:550.690 JLINK_WriteReg(R0, 0x00002800)
T7474 000:550.710 - 0.019ms returns 0
T7474 000:550.729 JLINK_WriteReg(R1, 0x00000400)
T7474 000:550.750 - 0.020ms returns 0
T7474 000:550.770 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:550.789 - 0.018ms returns 0
T7474 000:550.808 JLINK_WriteReg(R3, 0x00000000)
T7474 000:550.831 - 0.022ms returns 0
T7474 000:550.850 JLINK_WriteReg(R4, 0x00000000)
T7474 000:550.869 - 0.018ms returns 0
T7474 000:550.889 JLINK_WriteReg(R5, 0x00000000)
T7474 000:550.908 - 0.018ms returns 0
T7474 000:550.927 JLINK_WriteReg(R6, 0x00000000)
T7474 000:550.946 - 0.018ms returns 0
T7474 000:550.966 JLINK_WriteReg(R7, 0x00000000)
T7474 000:550.984 - 0.018ms returns 0
T7474 000:551.004 JLINK_WriteReg(R8, 0x00000000)
T7474 000:551.022 - 0.018ms returns 0
T7474 000:551.042 JLINK_WriteReg(R9, 0x20200290)
T7474 000:551.060 - 0.018ms returns 0
T7474 000:551.080 JLINK_WriteReg(R10, 0x00000000)
T7474 000:551.099 - 0.018ms returns 0
T7474 000:551.119 JLINK_WriteReg(R11, 0x00000000)
T7474 000:551.137 - 0.018ms returns 0
T7474 000:551.157 JLINK_WriteReg(R12, 0x00000000)
T7474 000:551.175 - 0.018ms returns 0
T7474 000:551.195 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:551.214 - 0.018ms returns 0
T7474 000:551.233 JLINK_WriteReg(R14, 0x20200001)
T7474 000:551.252 - 0.018ms returns 0
T7474 000:551.272 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:551.290 - 0.018ms returns 0
T7474 000:551.310 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:551.328 - 0.018ms returns 0
T7474 000:551.348 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:551.366 - 0.018ms returns 0
T7474 000:551.386 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:551.405 - 0.018ms returns 0
T7474 000:551.424 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:551.443 - 0.018ms returns 0
T7474 000:551.463 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:551.482 - 0.019ms returns 0x00000016
T7474 000:551.501 JLINK_Go()
T7474 000:551.523   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:554.974 - 3.472ms
T7474 000:555.000 JLINK_IsHalted()
T7474 000:557.809   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:558.531 - 3.530ms returns TRUE
T7474 000:558.557 JLINK_ReadReg(R15 (PC))
T7474 000:558.576 - 0.019ms returns 0x20200000
T7474 000:558.596 JLINK_ClrBPEx(BPHandle = 0x00000016)
T7474 000:558.615 - 0.018ms returns 0x00
T7474 000:558.634 JLINK_ReadReg(R0)
T7474 000:558.653 - 0.018ms returns 0x00000001
T7474 000:558.673 JLINK_HasError()
T7474 000:558.692 JLINK_WriteReg(R0, 0x00002800)
T7474 000:558.711 - 0.018ms returns 0
T7474 000:558.731 JLINK_WriteReg(R1, 0x00000400)
T7474 000:558.749 - 0.018ms returns 0
T7474 000:558.769 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:558.787 - 0.018ms returns 0
T7474 000:558.806 JLINK_WriteReg(R3, 0x00000000)
T7474 000:558.825 - 0.018ms returns 0
T7474 000:558.848 JLINK_WriteReg(R4, 0x00000000)
T7474 000:558.867 - 0.018ms returns 0
T7474 000:558.886 JLINK_WriteReg(R5, 0x00000000)
T7474 000:558.905 - 0.018ms returns 0
T7474 000:558.924 JLINK_WriteReg(R6, 0x00000000)
T7474 000:558.943 - 0.018ms returns 0
T7474 000:558.962 JLINK_WriteReg(R7, 0x00000000)
T7474 000:558.981 - 0.018ms returns 0
T7474 000:559.000 JLINK_WriteReg(R8, 0x00000000)
T7474 000:559.019 - 0.018ms returns 0
T7474 000:559.038 JLINK_WriteReg(R9, 0x20200290)
T7474 000:559.057 - 0.018ms returns 0
T7474 000:559.076 JLINK_WriteReg(R10, 0x00000000)
T7474 000:559.095 - 0.018ms returns 0
T7474 000:559.119 JLINK_WriteReg(R11, 0x00000000)
T7474 000:559.138 - 0.018ms returns 0
T7474 000:559.158 JLINK_WriteReg(R12, 0x00000000)
T7474 000:559.176 - 0.018ms returns 0
T7474 000:559.196 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:559.215 - 0.018ms returns 0
T7474 000:559.234 JLINK_WriteReg(R14, 0x20200001)
T7474 000:559.253 - 0.018ms returns 0
T7474 000:559.272 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:559.291 - 0.018ms returns 0
T7474 000:559.311 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:559.329 - 0.018ms returns 0
T7474 000:559.349 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:559.367 - 0.018ms returns 0
T7474 000:559.387 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:559.406 - 0.018ms returns 0
T7474 000:559.425 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:559.445 - 0.019ms returns 0
T7474 000:559.465 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:559.484 - 0.018ms returns 0x00000017
T7474 000:559.503 JLINK_Go()
T7474 000:559.524   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:563.111 - 3.607ms
T7474 000:563.138 JLINK_IsHalted()
T7474 000:563.888 - 0.748ms returns FALSE
T7474 000:563.945 JLINK_HasError()
T7474 000:566.407 JLINK_IsHalted()
T7474 000:569.124   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:569.896 - 3.487ms returns TRUE
T7474 000:569.921 JLINK_ReadReg(R15 (PC))
T7474 000:569.941 - 0.019ms returns 0x20200000
T7474 000:569.963 JLINK_ClrBPEx(BPHandle = 0x00000017)
T7474 000:569.982 - 0.018ms returns 0x00
T7474 000:570.003 JLINK_ReadReg(R0)
T7474 000:570.022 - 0.018ms returns 0x00000000
T7474 000:570.420 JLINK_HasError()
T7474 000:570.445 JLINK_WriteReg(R0, 0x00002C00)
T7474 000:570.464 - 0.019ms returns 0
T7474 000:570.483 JLINK_WriteReg(R1, 0x00000400)
T7474 000:570.502 - 0.018ms returns 0
T7474 000:570.521 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:570.540 - 0.018ms returns 0
T7474 000:570.559 JLINK_WriteReg(R3, 0x00000000)
T7474 000:570.577 - 0.018ms returns 0
T7474 000:570.596 JLINK_WriteReg(R4, 0x00000000)
T7474 000:570.615 - 0.018ms returns 0
T7474 000:570.634 JLINK_WriteReg(R5, 0x00000000)
T7474 000:570.652 - 0.018ms returns 0
T7474 000:570.672 JLINK_WriteReg(R6, 0x00000000)
T7474 000:570.690 - 0.018ms returns 0
T7474 000:570.709 JLINK_WriteReg(R7, 0x00000000)
T7474 000:570.728 - 0.018ms returns 0
T7474 000:570.747 JLINK_WriteReg(R8, 0x00000000)
T7474 000:570.765 - 0.018ms returns 0
T7474 000:570.785 JLINK_WriteReg(R9, 0x20200290)
T7474 000:570.803 - 0.018ms returns 0
T7474 000:570.822 JLINK_WriteReg(R10, 0x00000000)
T7474 000:570.841 - 0.018ms returns 0
T7474 000:570.860 JLINK_WriteReg(R11, 0x00000000)
T7474 000:570.878 - 0.018ms returns 0
T7474 000:570.897 JLINK_WriteReg(R12, 0x00000000)
T7474 000:570.916 - 0.018ms returns 0
T7474 000:570.935 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:570.954 - 0.018ms returns 0
T7474 000:570.973 JLINK_WriteReg(R14, 0x20200001)
T7474 000:570.991 - 0.018ms returns 0
T7474 000:571.011 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:571.029 - 0.018ms returns 0
T7474 000:571.048 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:571.067 - 0.018ms returns 0
T7474 000:571.086 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:571.104 - 0.018ms returns 0
T7474 000:571.124 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:571.142 - 0.018ms returns 0
T7474 000:571.161 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:571.180 - 0.018ms returns 0
T7474 000:571.199 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:571.218 - 0.018ms returns 0x00000018
T7474 000:571.237 JLINK_Go()
T7474 000:571.258   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:574.862 - 3.624ms
T7474 000:574.915 JLINK_IsHalted()
T7474 000:577.808   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:578.503 - 3.587ms returns TRUE
T7474 000:578.530 JLINK_ReadReg(R15 (PC))
T7474 000:578.550 - 0.020ms returns 0x20200000
T7474 000:578.570 JLINK_ClrBPEx(BPHandle = 0x00000018)
T7474 000:578.589 - 0.019ms returns 0x00
T7474 000:578.609 JLINK_ReadReg(R0)
T7474 000:578.628 - 0.018ms returns 0x00000001
T7474 000:578.648 JLINK_HasError()
T7474 000:578.668 JLINK_WriteReg(R0, 0x00002C00)
T7474 000:578.687 - 0.019ms returns 0
T7474 000:578.707 JLINK_WriteReg(R1, 0x00000400)
T7474 000:578.726 - 0.018ms returns 0
T7474 000:578.746 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:578.764 - 0.018ms returns 0
T7474 000:578.784 JLINK_WriteReg(R3, 0x00000000)
T7474 000:578.803 - 0.018ms returns 0
T7474 000:578.823 JLINK_WriteReg(R4, 0x00000000)
T7474 000:578.842 - 0.018ms returns 0
T7474 000:578.862 JLINK_WriteReg(R5, 0x00000000)
T7474 000:578.880 - 0.018ms returns 0
T7474 000:578.900 JLINK_WriteReg(R6, 0x00000000)
T7474 000:578.919 - 0.018ms returns 0
T7474 000:578.939 JLINK_WriteReg(R7, 0x00000000)
T7474 000:578.958 - 0.018ms returns 0
T7474 000:578.978 JLINK_WriteReg(R8, 0x00000000)
T7474 000:578.996 - 0.018ms returns 0
T7474 000:579.018 JLINK_WriteReg(R9, 0x20200290)
T7474 000:579.037 - 0.018ms returns 0
T7474 000:579.057 JLINK_WriteReg(R10, 0x00000000)
T7474 000:579.076 - 0.018ms returns 0
T7474 000:579.096 JLINK_WriteReg(R11, 0x00000000)
T7474 000:579.114 - 0.018ms returns 0
T7474 000:579.134 JLINK_WriteReg(R12, 0x00000000)
T7474 000:579.153 - 0.018ms returns 0
T7474 000:579.173 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:579.192 - 0.019ms returns 0
T7474 000:579.211 JLINK_WriteReg(R14, 0x20200001)
T7474 000:579.230 - 0.018ms returns 0
T7474 000:579.250 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:579.272 - 0.022ms returns 0
T7474 000:579.292 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:579.311 - 0.018ms returns 0
T7474 000:579.334 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:579.353 - 0.018ms returns 0
T7474 000:579.372 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:579.391 - 0.018ms returns 0
T7474 000:579.411 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:579.430 - 0.018ms returns 0
T7474 000:579.450 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:579.469 - 0.019ms returns 0x00000019
T7474 000:579.488 JLINK_Go()
T7474 000:579.510   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:582.975 - 3.486ms
T7474 000:583.002 JLINK_IsHalted()
T7474 000:583.775 - 0.772ms returns FALSE
T7474 000:583.801 JLINK_HasError()
T7474 000:585.202 JLINK_IsHalted()
T7474 000:587.848   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:588.662 - 3.459ms returns TRUE
T7474 000:588.722 JLINK_ReadReg(R15 (PC))
T7474 000:588.769 - 0.047ms returns 0x20200000
T7474 000:588.816 JLINK_ClrBPEx(BPHandle = 0x00000019)
T7474 000:588.860 - 0.044ms returns 0x00
T7474 000:588.907 JLINK_ReadReg(R0)
T7474 000:588.933 - 0.026ms returns 0x00000000
T7474 000:589.427 JLINK_HasError()
T7474 000:589.454 JLINK_WriteReg(R0, 0x00003000)
T7474 000:589.476 - 0.022ms returns 0
T7474 000:589.499 JLINK_WriteReg(R1, 0x00000400)
T7474 000:589.520 - 0.021ms returns 0
T7474 000:589.542 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:589.563 - 0.020ms returns 0
T7474 000:589.585 JLINK_WriteReg(R3, 0x00000000)
T7474 000:589.606 - 0.020ms returns 0
T7474 000:589.628 JLINK_WriteReg(R4, 0x00000000)
T7474 000:589.649 - 0.020ms returns 0
T7474 000:589.671 JLINK_WriteReg(R5, 0x00000000)
T7474 000:589.692 - 0.021ms returns 0
T7474 000:589.714 JLINK_WriteReg(R6, 0x00000000)
T7474 000:589.735 - 0.021ms returns 0
T7474 000:589.758 JLINK_WriteReg(R7, 0x00000000)
T7474 000:589.779 - 0.021ms returns 0
T7474 000:589.801 JLINK_WriteReg(R8, 0x00000000)
T7474 000:589.822 - 0.020ms returns 0
T7474 000:589.844 JLINK_WriteReg(R9, 0x20200290)
T7474 000:589.865 - 0.020ms returns 0
T7474 000:589.887 JLINK_WriteReg(R10, 0x00000000)
T7474 000:589.912 - 0.025ms returns 0
T7474 000:589.935 JLINK_WriteReg(R11, 0x00000000)
T7474 000:589.957 - 0.021ms returns 0
T7474 000:589.980 JLINK_WriteReg(R12, 0x00000000)
T7474 000:590.001 - 0.021ms returns 0
T7474 000:590.025 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:590.047 - 0.022ms returns 0
T7474 000:590.069 JLINK_WriteReg(R14, 0x20200001)
T7474 000:590.091 - 0.022ms returns 0
T7474 000:590.115 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:590.137 - 0.022ms returns 0
T7474 000:590.160 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:590.182 - 0.022ms returns 0
T7474 000:590.205 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:590.226 - 0.021ms returns 0
T7474 000:590.250 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:590.271 - 0.021ms returns 0
T7474 000:590.294 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:590.316 - 0.021ms returns 0
T7474 000:590.340 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:590.362 - 0.022ms returns 0x0000001A
T7474 000:590.385 JLINK_Go()
T7474 000:590.411   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:594.072 - 3.687ms
T7474 000:594.100 JLINK_IsHalted()
T7474 000:596.788   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:597.554 - 3.453ms returns TRUE
T7474 000:597.619 JLINK_ReadReg(R15 (PC))
T7474 000:597.662 - 0.043ms returns 0x20200000
T7474 000:597.708 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T7474 000:597.753 - 0.044ms returns 0x00
T7474 000:597.805 JLINK_ReadReg(R0)
T7474 000:597.855 - 0.049ms returns 0x00000001
T7474 000:597.900 JLINK_HasError()
T7474 000:597.946 JLINK_WriteReg(R0, 0x00003000)
T7474 000:597.987 - 0.040ms returns 0
T7474 000:598.023 JLINK_WriteReg(R1, 0x00000400)
T7474 000:598.042 - 0.018ms returns 0
T7474 000:598.063 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:598.082 - 0.018ms returns 0
T7474 000:598.103 JLINK_WriteReg(R3, 0x00000000)
T7474 000:598.122 - 0.019ms returns 0
T7474 000:598.144 JLINK_WriteReg(R4, 0x00000000)
T7474 000:598.162 - 0.018ms returns 0
T7474 000:598.184 JLINK_WriteReg(R5, 0x00000000)
T7474 000:598.203 - 0.018ms returns 0
T7474 000:598.224 JLINK_WriteReg(R6, 0x00000000)
T7474 000:598.243 - 0.018ms returns 0
T7474 000:598.264 JLINK_WriteReg(R7, 0x00000000)
T7474 000:598.283 - 0.018ms returns 0
T7474 000:598.304 JLINK_WriteReg(R8, 0x00000000)
T7474 000:598.323 - 0.018ms returns 0
T7474 000:598.344 JLINK_WriteReg(R9, 0x20200290)
T7474 000:598.363 - 0.018ms returns 0
T7474 000:598.385 JLINK_WriteReg(R10, 0x00000000)
T7474 000:598.403 - 0.018ms returns 0
T7474 000:598.425 JLINK_WriteReg(R11, 0x00000000)
T7474 000:598.443 - 0.018ms returns 0
T7474 000:598.465 JLINK_WriteReg(R12, 0x00000000)
T7474 000:598.484 - 0.018ms returns 0
T7474 000:598.505 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:598.524 - 0.019ms returns 0
T7474 000:598.545 JLINK_WriteReg(R14, 0x20200001)
T7474 000:598.564 - 0.019ms returns 0
T7474 000:598.586 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:598.604 - 0.018ms returns 0
T7474 000:598.626 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:598.645 - 0.019ms returns 0
T7474 000:598.666 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:598.685 - 0.018ms returns 0
T7474 000:598.707 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:598.725 - 0.018ms returns 0
T7474 000:598.747 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:598.766 - 0.018ms returns 0
T7474 000:598.787 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:598.806 - 0.019ms returns 0x0000001B
T7474 000:598.827 JLINK_Go()
T7474 000:598.849   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:602.219 - 3.391ms
T7474 000:602.249 JLINK_IsHalted()
T7474 000:603.010 - 0.760ms returns FALSE
T7474 000:603.039 JLINK_HasError()
T7474 000:605.069 JLINK_IsHalted()
T7474 000:607.859   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:608.663 - 3.593ms returns TRUE
T7474 000:608.720 JLINK_ReadReg(R15 (PC))
T7474 000:608.762 - 0.042ms returns 0x20200000
T7474 000:608.805 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T7474 000:608.845 - 0.040ms returns 0x00
T7474 000:608.887 JLINK_ReadReg(R0)
T7474 000:608.938 - 0.051ms returns 0x00000000
T7474 000:609.548 JLINK_HasError()
T7474 000:609.575 JLINK_WriteReg(R0, 0x00003400)
T7474 000:609.594 - 0.019ms returns 0
T7474 000:609.614 JLINK_WriteReg(R1, 0x00000400)
T7474 000:609.633 - 0.018ms returns 0
T7474 000:609.653 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:609.671 - 0.018ms returns 0
T7474 000:609.691 JLINK_WriteReg(R3, 0x00000000)
T7474 000:609.710 - 0.018ms returns 0
T7474 000:609.730 JLINK_WriteReg(R4, 0x00000000)
T7474 000:609.749 - 0.018ms returns 0
T7474 000:609.769 JLINK_WriteReg(R5, 0x00000000)
T7474 000:609.787 - 0.018ms returns 0
T7474 000:609.807 JLINK_WriteReg(R6, 0x00000000)
T7474 000:609.826 - 0.018ms returns 0
T7474 000:609.846 JLINK_WriteReg(R7, 0x00000000)
T7474 000:609.864 - 0.018ms returns 0
T7474 000:609.884 JLINK_WriteReg(R8, 0x00000000)
T7474 000:609.903 - 0.018ms returns 0
T7474 000:609.922 JLINK_WriteReg(R9, 0x20200290)
T7474 000:609.945 - 0.022ms returns 0
T7474 000:609.965 JLINK_WriteReg(R10, 0x00000000)
T7474 000:609.984 - 0.018ms returns 0
T7474 000:610.003 JLINK_WriteReg(R11, 0x00000000)
T7474 000:610.022 - 0.018ms returns 0
T7474 000:610.042 JLINK_WriteReg(R12, 0x00000000)
T7474 000:610.061 - 0.018ms returns 0
T7474 000:610.080 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:610.099 - 0.019ms returns 0
T7474 000:610.119 JLINK_WriteReg(R14, 0x20200001)
T7474 000:610.139 - 0.020ms returns 0
T7474 000:610.159 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:610.178 - 0.018ms returns 0
T7474 000:610.198 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:610.217 - 0.018ms returns 0
T7474 000:610.236 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:610.255 - 0.018ms returns 0
T7474 000:610.275 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:610.294 - 0.018ms returns 0
T7474 000:610.313 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:610.332 - 0.018ms returns 0
T7474 000:610.358 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:610.377 - 0.019ms returns 0x0000001C
T7474 000:610.397 JLINK_Go()
T7474 000:610.418   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:613.985 - 3.587ms
T7474 000:614.041 JLINK_IsHalted()
T7474 000:616.887   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:617.642 - 3.601ms returns TRUE
T7474 000:617.668 JLINK_ReadReg(R15 (PC))
T7474 000:617.688 - 0.019ms returns 0x20200000
T7474 000:617.708 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T7474 000:617.727 - 0.018ms returns 0x00
T7474 000:617.746 JLINK_ReadReg(R0)
T7474 000:617.765 - 0.018ms returns 0x00000001
T7474 000:617.785 JLINK_HasError()
T7474 000:617.805 JLINK_WriteReg(R0, 0x00003400)
T7474 000:617.824 - 0.019ms returns 0
T7474 000:617.843 JLINK_WriteReg(R1, 0x00000400)
T7474 000:617.862 - 0.018ms returns 0
T7474 000:617.881 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:617.900 - 0.018ms returns 0
T7474 000:617.919 JLINK_WriteReg(R3, 0x00000000)
T7474 000:617.938 - 0.018ms returns 0
T7474 000:617.962 JLINK_WriteReg(R4, 0x00000000)
T7474 000:617.980 - 0.018ms returns 0
T7474 000:618.000 JLINK_WriteReg(R5, 0x00000000)
T7474 000:618.019 - 0.018ms returns 0
T7474 000:618.038 JLINK_WriteReg(R6, 0x00000000)
T7474 000:618.057 - 0.018ms returns 0
T7474 000:618.077 JLINK_WriteReg(R7, 0x00000000)
T7474 000:618.096 - 0.018ms returns 0
T7474 000:618.116 JLINK_WriteReg(R8, 0x00000000)
T7474 000:618.134 - 0.018ms returns 0
T7474 000:618.154 JLINK_WriteReg(R9, 0x20200290)
T7474 000:618.173 - 0.018ms returns 0
T7474 000:618.192 JLINK_WriteReg(R10, 0x00000000)
T7474 000:618.211 - 0.018ms returns 0
T7474 000:618.235 JLINK_WriteReg(R11, 0x00000000)
T7474 000:618.254 - 0.019ms returns 0
T7474 000:618.274 JLINK_WriteReg(R12, 0x00000000)
T7474 000:618.293 - 0.018ms returns 0
T7474 000:618.313 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:618.332 - 0.018ms returns 0
T7474 000:618.351 JLINK_WriteReg(R14, 0x20200001)
T7474 000:618.370 - 0.018ms returns 0
T7474 000:618.390 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:618.409 - 0.018ms returns 0
T7474 000:618.428 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:618.447 - 0.018ms returns 0
T7474 000:618.467 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:618.485 - 0.018ms returns 0
T7474 000:618.505 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:618.524 - 0.018ms returns 0
T7474 000:618.543 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:618.562 - 0.018ms returns 0
T7474 000:618.582 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:618.601 - 0.019ms returns 0x0000001D
T7474 000:618.621 JLINK_Go()
T7474 000:618.642   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:622.098 - 3.477ms
T7474 000:622.125 JLINK_IsHalted()
T7474 000:622.886 - 0.760ms returns FALSE
T7474 000:622.911 JLINK_HasError()
T7474 000:625.128 JLINK_IsHalted()
T7474 000:627.991   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:628.764 - 3.635ms returns TRUE
T7474 000:628.793 JLINK_ReadReg(R15 (PC))
T7474 000:628.813 - 0.020ms returns 0x20200000
T7474 000:628.834 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T7474 000:628.853 - 0.019ms returns 0x00
T7474 000:628.874 JLINK_ReadReg(R0)
T7474 000:628.893 - 0.018ms returns 0x00000000
T7474 000:629.289 JLINK_HasError()
T7474 000:629.314 JLINK_WriteReg(R0, 0x00003800)
T7474 000:629.334 - 0.019ms returns 0
T7474 000:629.353 JLINK_WriteReg(R1, 0x00000400)
T7474 000:629.372 - 0.018ms returns 0
T7474 000:629.392 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:629.410 - 0.018ms returns 0
T7474 000:629.430 JLINK_WriteReg(R3, 0x00000000)
T7474 000:629.449 - 0.018ms returns 0
T7474 000:629.470 JLINK_WriteReg(R4, 0x00000000)
T7474 000:629.489 - 0.018ms returns 0
T7474 000:629.508 JLINK_WriteReg(R5, 0x00000000)
T7474 000:629.527 - 0.018ms returns 0
T7474 000:629.547 JLINK_WriteReg(R6, 0x00000000)
T7474 000:629.565 - 0.018ms returns 0
T7474 000:629.585 JLINK_WriteReg(R7, 0x00000000)
T7474 000:629.603 - 0.018ms returns 0
T7474 000:629.623 JLINK_WriteReg(R8, 0x00000000)
T7474 000:629.641 - 0.018ms returns 0
T7474 000:629.667 JLINK_WriteReg(R9, 0x20200290)
T7474 000:629.686 - 0.018ms returns 0
T7474 000:629.705 JLINK_WriteReg(R10, 0x00000000)
T7474 000:629.724 - 0.018ms returns 0
T7474 000:629.744 JLINK_WriteReg(R11, 0x00000000)
T7474 000:629.762 - 0.018ms returns 0
T7474 000:629.782 JLINK_WriteReg(R12, 0x00000000)
T7474 000:629.800 - 0.018ms returns 0
T7474 000:629.820 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:629.839 - 0.018ms returns 0
T7474 000:629.858 JLINK_WriteReg(R14, 0x20200001)
T7474 000:629.877 - 0.018ms returns 0
T7474 000:629.897 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:629.915 - 0.018ms returns 0
T7474 000:629.935 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:629.954 - 0.018ms returns 0
T7474 000:629.973 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:629.992 - 0.018ms returns 0
T7474 000:630.016 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:630.034 - 0.018ms returns 0
T7474 000:630.054 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:630.072 - 0.018ms returns 0
T7474 000:630.092 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:630.111 - 0.019ms returns 0x0000001E
T7474 000:630.130 JLINK_Go()
T7474 000:630.152   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:633.511 - 3.380ms
T7474 000:633.538 JLINK_IsHalted()
T7474 000:636.374   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:637.154 - 3.615ms returns TRUE
T7474 000:637.180 JLINK_ReadReg(R15 (PC))
T7474 000:637.200 - 0.019ms returns 0x20200000
T7474 000:637.220 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T7474 000:637.238 - 0.018ms returns 0x00
T7474 000:637.258 JLINK_ReadReg(R0)
T7474 000:637.277 - 0.018ms returns 0x00000001
T7474 000:637.296 JLINK_HasError()
T7474 000:637.316 JLINK_WriteReg(R0, 0x00003800)
T7474 000:637.335 - 0.018ms returns 0
T7474 000:637.355 JLINK_WriteReg(R1, 0x00000400)
T7474 000:637.373 - 0.018ms returns 0
T7474 000:637.393 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:637.411 - 0.018ms returns 0
T7474 000:637.431 JLINK_WriteReg(R3, 0x00000000)
T7474 000:637.449 - 0.018ms returns 0
T7474 000:637.469 JLINK_WriteReg(R4, 0x00000000)
T7474 000:637.487 - 0.018ms returns 0
T7474 000:637.507 JLINK_WriteReg(R5, 0x00000000)
T7474 000:637.525 - 0.018ms returns 0
T7474 000:637.545 JLINK_WriteReg(R6, 0x00000000)
T7474 000:637.563 - 0.018ms returns 0
T7474 000:637.583 JLINK_WriteReg(R7, 0x00000000)
T7474 000:637.602 - 0.018ms returns 0
T7474 000:637.621 JLINK_WriteReg(R8, 0x00000000)
T7474 000:637.640 - 0.018ms returns 0
T7474 000:637.659 JLINK_WriteReg(R9, 0x20200290)
T7474 000:637.678 - 0.018ms returns 0
T7474 000:637.697 JLINK_WriteReg(R10, 0x00000000)
T7474 000:637.716 - 0.018ms returns 0
T7474 000:637.735 JLINK_WriteReg(R11, 0x00000000)
T7474 000:637.754 - 0.018ms returns 0
T7474 000:637.773 JLINK_WriteReg(R12, 0x00000000)
T7474 000:637.792 - 0.018ms returns 0
T7474 000:637.811 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:637.830 - 0.018ms returns 0
T7474 000:637.849 JLINK_WriteReg(R14, 0x20200001)
T7474 000:637.868 - 0.018ms returns 0
T7474 000:637.888 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:637.906 - 0.018ms returns 0
T7474 000:637.926 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:637.944 - 0.018ms returns 0
T7474 000:637.967 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:637.986 - 0.018ms returns 0
T7474 000:638.009 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:638.028 - 0.018ms returns 0
T7474 000:638.048 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:638.066 - 0.018ms returns 0
T7474 000:638.086 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:638.105 - 0.019ms returns 0x0000001F
T7474 000:638.124 JLINK_Go()
T7474 000:638.148   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:641.737 - 3.612ms
T7474 000:641.764 JLINK_IsHalted()
T7474 000:642.517 - 0.753ms returns FALSE
T7474 000:642.544 JLINK_HasError()
T7474 000:644.225 JLINK_IsHalted()
T7474 000:647.125   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:647.921 - 3.695ms returns TRUE
T7474 000:647.979 JLINK_ReadReg(R15 (PC))
T7474 000:648.017 - 0.038ms returns 0x20200000
T7474 000:648.037 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T7474 000:648.056 - 0.018ms returns 0x00
T7474 000:648.075 JLINK_ReadReg(R0)
T7474 000:648.094 - 0.018ms returns 0x00000000
T7474 000:648.491 JLINK_HasError()
T7474 000:648.515 JLINK_WriteReg(R0, 0x00003C00)
T7474 000:648.535 - 0.019ms returns 0
T7474 000:648.555 JLINK_WriteReg(R1, 0x00000400)
T7474 000:648.573 - 0.018ms returns 0
T7474 000:648.593 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:648.612 - 0.018ms returns 0
T7474 000:648.631 JLINK_WriteReg(R3, 0x00000000)
T7474 000:648.650 - 0.018ms returns 0
T7474 000:648.669 JLINK_WriteReg(R4, 0x00000000)
T7474 000:648.688 - 0.018ms returns 0
T7474 000:648.708 JLINK_WriteReg(R5, 0x00000000)
T7474 000:648.726 - 0.018ms returns 0
T7474 000:648.746 JLINK_WriteReg(R6, 0x00000000)
T7474 000:648.764 - 0.018ms returns 0
T7474 000:648.784 JLINK_WriteReg(R7, 0x00000000)
T7474 000:648.803 - 0.018ms returns 0
T7474 000:648.822 JLINK_WriteReg(R8, 0x00000000)
T7474 000:648.841 - 0.018ms returns 0
T7474 000:648.860 JLINK_WriteReg(R9, 0x20200290)
T7474 000:648.879 - 0.018ms returns 0
T7474 000:648.898 JLINK_WriteReg(R10, 0x00000000)
T7474 000:648.917 - 0.018ms returns 0
T7474 000:648.937 JLINK_WriteReg(R11, 0x00000000)
T7474 000:648.955 - 0.018ms returns 0
T7474 000:648.975 JLINK_WriteReg(R12, 0x00000000)
T7474 000:648.993 - 0.018ms returns 0
T7474 000:649.013 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:649.036 - 0.022ms returns 0
T7474 000:649.056 JLINK_WriteReg(R14, 0x20200001)
T7474 000:649.074 - 0.018ms returns 0
T7474 000:649.094 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:649.112 - 0.018ms returns 0
T7474 000:649.132 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:649.150 - 0.018ms returns 0
T7474 000:649.170 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:649.188 - 0.018ms returns 0
T7474 000:649.208 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:649.226 - 0.018ms returns 0
T7474 000:649.246 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:649.264 - 0.018ms returns 0
T7474 000:649.284 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:649.303 - 0.018ms returns 0x00000020
T7474 000:649.322 JLINK_Go()
T7474 000:649.343   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:652.927 - 3.604ms
T7474 000:652.953 JLINK_IsHalted()
T7474 000:655.633   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:656.392 - 3.438ms returns TRUE
T7474 000:656.421 JLINK_ReadReg(R15 (PC))
T7474 000:656.441 - 0.019ms returns 0x20200000
T7474 000:656.462 JLINK_ClrBPEx(BPHandle = 0x00000020)
T7474 000:656.481 - 0.018ms returns 0x00
T7474 000:656.502 JLINK_ReadReg(R0)
T7474 000:656.521 - 0.018ms returns 0x00000001
T7474 000:656.542 JLINK_HasError()
T7474 000:656.563 JLINK_WriteReg(R0, 0x00003C00)
T7474 000:656.582 - 0.018ms returns 0
T7474 000:656.603 JLINK_WriteReg(R1, 0x00000400)
T7474 000:656.621 - 0.018ms returns 0
T7474 000:656.642 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:656.660 - 0.018ms returns 0
T7474 000:656.681 JLINK_WriteReg(R3, 0x00000000)
T7474 000:656.700 - 0.018ms returns 0
T7474 000:656.721 JLINK_WriteReg(R4, 0x00000000)
T7474 000:656.743 - 0.022ms returns 0
T7474 000:656.764 JLINK_WriteReg(R5, 0x00000000)
T7474 000:656.783 - 0.018ms returns 0
T7474 000:656.804 JLINK_WriteReg(R6, 0x00000000)
T7474 000:656.822 - 0.018ms returns 0
T7474 000:656.843 JLINK_WriteReg(R7, 0x00000000)
T7474 000:656.862 - 0.018ms returns 0
T7474 000:656.883 JLINK_WriteReg(R8, 0x00000000)
T7474 000:656.901 - 0.018ms returns 0
T7474 000:656.922 JLINK_WriteReg(R9, 0x20200290)
T7474 000:656.940 - 0.018ms returns 0
T7474 000:656.962 JLINK_WriteReg(R10, 0x00000000)
T7474 000:656.980 - 0.018ms returns 0
T7474 000:657.001 JLINK_WriteReg(R11, 0x00000000)
T7474 000:657.020 - 0.019ms returns 0
T7474 000:657.043 JLINK_WriteReg(R12, 0x00000000)
T7474 000:657.061 - 0.018ms returns 0
T7474 000:657.083 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:657.101 - 0.018ms returns 0
T7474 000:657.123 JLINK_WriteReg(R14, 0x20200001)
T7474 000:657.141 - 0.018ms returns 0
T7474 000:657.163 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:657.182 - 0.018ms returns 0
T7474 000:657.203 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:657.221 - 0.018ms returns 0
T7474 000:657.242 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:657.261 - 0.018ms returns 0
T7474 000:657.282 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:657.300 - 0.018ms returns 0
T7474 000:657.321 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:657.340 - 0.018ms returns 0
T7474 000:657.361 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:657.380 - 0.018ms returns 0x00000021
T7474 000:657.401 JLINK_Go()
T7474 000:657.422   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:660.984 - 3.582ms
T7474 000:661.014 JLINK_IsHalted()
T7474 000:661.760 - 0.745ms returns FALSE
T7474 000:661.788 JLINK_HasError()
T7474 000:664.436 JLINK_IsHalted()
T7474 000:667.263   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:668.065 - 3.628ms returns TRUE
T7474 000:668.122 JLINK_ReadReg(R15 (PC))
T7474 000:668.161 - 0.039ms returns 0x20200000
T7474 000:668.181 JLINK_ClrBPEx(BPHandle = 0x00000021)
T7474 000:668.199 - 0.018ms returns 0x00
T7474 000:668.219 JLINK_ReadReg(R0)
T7474 000:668.237 - 0.018ms returns 0x00000000
T7474 000:668.689 JLINK_HasError()
T7474 000:668.714 JLINK_WriteReg(R0, 0x00004000)
T7474 000:668.734 - 0.019ms returns 0
T7474 000:668.753 JLINK_WriteReg(R1, 0x00000400)
T7474 000:668.771 - 0.018ms returns 0
T7474 000:668.791 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:668.809 - 0.018ms returns 0
T7474 000:668.828 JLINK_WriteReg(R3, 0x00000000)
T7474 000:668.847 - 0.018ms returns 0
T7474 000:668.866 JLINK_WriteReg(R4, 0x00000000)
T7474 000:668.885 - 0.018ms returns 0
T7474 000:668.904 JLINK_WriteReg(R5, 0x00000000)
T7474 000:668.922 - 0.018ms returns 0
T7474 000:668.942 JLINK_WriteReg(R6, 0x00000000)
T7474 000:668.960 - 0.018ms returns 0
T7474 000:668.980 JLINK_WriteReg(R7, 0x00000000)
T7474 000:668.998 - 0.018ms returns 0
T7474 000:669.017 JLINK_WriteReg(R8, 0x00000000)
T7474 000:669.036 - 0.018ms returns 0
T7474 000:669.062 JLINK_WriteReg(R9, 0x20200290)
T7474 000:669.085 - 0.022ms returns 0
T7474 000:669.105 JLINK_WriteReg(R10, 0x00000000)
T7474 000:669.123 - 0.018ms returns 0
T7474 000:669.142 JLINK_WriteReg(R11, 0x00000000)
T7474 000:669.160 - 0.018ms returns 0
T7474 000:669.180 JLINK_WriteReg(R12, 0x00000000)
T7474 000:669.198 - 0.018ms returns 0
T7474 000:669.217 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:669.235 - 0.018ms returns 0
T7474 000:669.255 JLINK_WriteReg(R14, 0x20200001)
T7474 000:669.273 - 0.018ms returns 0
T7474 000:669.292 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:669.310 - 0.018ms returns 0
T7474 000:669.330 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:669.348 - 0.018ms returns 0
T7474 000:669.367 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:669.385 - 0.018ms returns 0
T7474 000:669.404 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:669.423 - 0.018ms returns 0
T7474 000:669.442 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:669.460 - 0.018ms returns 0
T7474 000:669.479 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:669.498 - 0.018ms returns 0x00000022
T7474 000:669.517 JLINK_Go()
T7474 000:669.538   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:673.106 - 3.588ms
T7474 000:673.132 JLINK_IsHalted()
T7474 000:675.863   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:676.659 - 3.526ms returns TRUE
T7474 000:676.717 JLINK_ReadReg(R15 (PC))
T7474 000:676.761 - 0.043ms returns 0x20200000
T7474 000:676.805 JLINK_ClrBPEx(BPHandle = 0x00000022)
T7474 000:676.846 - 0.041ms returns 0x00
T7474 000:676.890 JLINK_ReadReg(R0)
T7474 000:676.931 - 0.041ms returns 0x00000001
T7474 000:676.976 JLINK_HasError()
T7474 000:677.023 JLINK_WriteReg(R0, 0x00004000)
T7474 000:677.067 - 0.043ms returns 0
T7474 000:677.103 JLINK_WriteReg(R1, 0x00000400)
T7474 000:677.121 - 0.018ms returns 0
T7474 000:677.141 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:677.159 - 0.018ms returns 0
T7474 000:677.179 JLINK_WriteReg(R3, 0x00000000)
T7474 000:677.197 - 0.018ms returns 0
T7474 000:677.216 JLINK_WriteReg(R4, 0x00000000)
T7474 000:677.235 - 0.018ms returns 0
T7474 000:677.254 JLINK_WriteReg(R5, 0x00000000)
T7474 000:677.272 - 0.018ms returns 0
T7474 000:677.292 JLINK_WriteReg(R6, 0x00000000)
T7474 000:677.311 - 0.018ms returns 0
T7474 000:677.330 JLINK_WriteReg(R7, 0x00000000)
T7474 000:677.348 - 0.018ms returns 0
T7474 000:677.368 JLINK_WriteReg(R8, 0x00000000)
T7474 000:677.386 - 0.018ms returns 0
T7474 000:677.406 JLINK_WriteReg(R9, 0x20200290)
T7474 000:677.424 - 0.018ms returns 0
T7474 000:677.443 JLINK_WriteReg(R10, 0x00000000)
T7474 000:677.461 - 0.018ms returns 0
T7474 000:677.481 JLINK_WriteReg(R11, 0x00000000)
T7474 000:677.499 - 0.018ms returns 0
T7474 000:677.519 JLINK_WriteReg(R12, 0x00000000)
T7474 000:677.537 - 0.018ms returns 0
T7474 000:677.557 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:677.575 - 0.018ms returns 0
T7474 000:677.595 JLINK_WriteReg(R14, 0x20200001)
T7474 000:677.613 - 0.018ms returns 0
T7474 000:677.637 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:677.655 - 0.018ms returns 0
T7474 000:677.675 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:677.693 - 0.018ms returns 0
T7474 000:677.713 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:677.731 - 0.018ms returns 0
T7474 000:677.751 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:677.769 - 0.018ms returns 0
T7474 000:677.789 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:677.807 - 0.018ms returns 0
T7474 000:677.827 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:677.845 - 0.018ms returns 0x00000023
T7474 000:677.865 JLINK_Go()
T7474 000:677.885   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:681.248 - 3.382ms
T7474 000:681.306 JLINK_IsHalted()
T7474 000:682.049 - 0.743ms returns FALSE
T7474 000:682.076 JLINK_HasError()
T7474 000:683.157 JLINK_IsHalted()
T7474 000:685.990   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:686.800 - 3.642ms returns TRUE
T7474 000:686.855 JLINK_ReadReg(R15 (PC))
T7474 000:686.898 - 0.042ms returns 0x20200000
T7474 000:686.940 JLINK_ClrBPEx(BPHandle = 0x00000023)
T7474 000:686.990 - 0.050ms returns 0x00
T7474 000:687.033 JLINK_ReadReg(R0)
T7474 000:687.072 - 0.039ms returns 0x00000000
T7474 000:687.662 JLINK_HasError()
T7474 000:687.688 JLINK_WriteReg(R0, 0x00004400)
T7474 000:687.708 - 0.019ms returns 0
T7474 000:687.727 JLINK_WriteReg(R1, 0x00000400)
T7474 000:687.746 - 0.018ms returns 0
T7474 000:687.765 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:687.783 - 0.018ms returns 0
T7474 000:687.803 JLINK_WriteReg(R3, 0x00000000)
T7474 000:687.821 - 0.018ms returns 0
T7474 000:687.841 JLINK_WriteReg(R4, 0x00000000)
T7474 000:687.859 - 0.018ms returns 0
T7474 000:687.878 JLINK_WriteReg(R5, 0x00000000)
T7474 000:687.897 - 0.018ms returns 0
T7474 000:687.916 JLINK_WriteReg(R6, 0x00000000)
T7474 000:687.935 - 0.018ms returns 0
T7474 000:687.954 JLINK_WriteReg(R7, 0x00000000)
T7474 000:687.972 - 0.018ms returns 0
T7474 000:687.992 JLINK_WriteReg(R8, 0x00000000)
T7474 000:688.010 - 0.018ms returns 0
T7474 000:688.029 JLINK_WriteReg(R9, 0x20200290)
T7474 000:688.048 - 0.018ms returns 0
T7474 000:688.067 JLINK_WriteReg(R10, 0x00000000)
T7474 000:688.088 - 0.020ms returns 0
T7474 000:688.109 JLINK_WriteReg(R11, 0x00000000)
T7474 000:688.128 - 0.018ms returns 0
T7474 000:688.147 JLINK_WriteReg(R12, 0x00000000)
T7474 000:688.166 - 0.018ms returns 0
T7474 000:688.185 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:688.231 - 0.046ms returns 0
T7474 000:688.251 JLINK_WriteReg(R14, 0x20200001)
T7474 000:688.269 - 0.018ms returns 0
T7474 000:688.289 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:688.307 - 0.018ms returns 0
T7474 000:688.327 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:688.347 - 0.019ms returns 0
T7474 000:688.367 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:688.385 - 0.018ms returns 0
T7474 000:688.405 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:688.423 - 0.018ms returns 0
T7474 000:688.443 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:688.461 - 0.018ms returns 0
T7474 000:688.481 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:688.500 - 0.019ms returns 0x00000024
T7474 000:688.519 JLINK_Go()
T7474 000:688.541   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:691.972 - 3.452ms
T7474 000:691.999 JLINK_IsHalted()
T7474 000:694.800   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:695.517 - 3.517ms returns TRUE
T7474 000:695.542 JLINK_ReadReg(R15 (PC))
T7474 000:695.562 - 0.019ms returns 0x20200000
T7474 000:695.581 JLINK_ClrBPEx(BPHandle = 0x00000024)
T7474 000:695.600 - 0.018ms returns 0x00
T7474 000:695.620 JLINK_ReadReg(R0)
T7474 000:695.638 - 0.018ms returns 0x00000001
T7474 000:695.658 JLINK_HasError()
T7474 000:695.677 JLINK_WriteReg(R0, 0x00004400)
T7474 000:695.696 - 0.018ms returns 0
T7474 000:695.715 JLINK_WriteReg(R1, 0x00000400)
T7474 000:695.734 - 0.018ms returns 0
T7474 000:695.753 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:695.772 - 0.018ms returns 0
T7474 000:695.791 JLINK_WriteReg(R3, 0x00000000)
T7474 000:695.809 - 0.018ms returns 0
T7474 000:695.829 JLINK_WriteReg(R4, 0x00000000)
T7474 000:695.847 - 0.018ms returns 0
T7474 000:695.866 JLINK_WriteReg(R5, 0x00000000)
T7474 000:695.885 - 0.018ms returns 0
T7474 000:695.904 JLINK_WriteReg(R6, 0x00000000)
T7474 000:695.922 - 0.018ms returns 0
T7474 000:695.942 JLINK_WriteReg(R7, 0x00000000)
T7474 000:695.960 - 0.018ms returns 0
T7474 000:695.979 JLINK_WriteReg(R8, 0x00000000)
T7474 000:695.998 - 0.018ms returns 0
T7474 000:696.017 JLINK_WriteReg(R9, 0x20200290)
T7474 000:696.035 - 0.018ms returns 0
T7474 000:696.055 JLINK_WriteReg(R10, 0x00000000)
T7474 000:696.073 - 0.018ms returns 0
T7474 000:696.092 JLINK_WriteReg(R11, 0x00000000)
T7474 000:696.115 - 0.022ms returns 0
T7474 000:696.134 JLINK_WriteReg(R12, 0x00000000)
T7474 000:696.153 - 0.018ms returns 0
T7474 000:696.172 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:696.191 - 0.018ms returns 0
T7474 000:696.210 JLINK_WriteReg(R14, 0x20200001)
T7474 000:696.229 - 0.018ms returns 0
T7474 000:696.248 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:696.266 - 0.018ms returns 0
T7474 000:696.286 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:696.305 - 0.018ms returns 0
T7474 000:696.324 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:696.348 - 0.023ms returns 0
T7474 000:696.367 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:696.386 - 0.018ms returns 0
T7474 000:696.405 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:696.424 - 0.018ms returns 0
T7474 000:696.443 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:696.462 - 0.018ms returns 0x00000025
T7474 000:696.481 JLINK_Go()
T7474 000:696.502   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:699.974 - 3.492ms
T7474 000:700.000 JLINK_IsHalted()
T7474 000:700.760 - 0.759ms returns FALSE
T7474 000:700.787 JLINK_HasError()
T7474 000:702.233 JLINK_IsHalted()
T7474 000:704.990   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:705.797 - 3.563ms returns TRUE
T7474 000:705.854 JLINK_ReadReg(R15 (PC))
T7474 000:705.896 - 0.042ms returns 0x20200000
T7474 000:705.939 JLINK_ClrBPEx(BPHandle = 0x00000025)
T7474 000:705.978 - 0.039ms returns 0x00
T7474 000:706.020 JLINK_ReadReg(R0)
T7474 000:706.070 - 0.049ms returns 0x00000000
T7474 000:706.535 JLINK_HasError()
T7474 000:706.559 JLINK_WriteReg(R0, 0x00004800)
T7474 000:706.579 - 0.019ms returns 0
T7474 000:706.598 JLINK_WriteReg(R1, 0x00000400)
T7474 000:706.617 - 0.018ms returns 0
T7474 000:706.636 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:706.655 - 0.018ms returns 0
T7474 000:706.674 JLINK_WriteReg(R3, 0x00000000)
T7474 000:706.692 - 0.018ms returns 0
T7474 000:706.712 JLINK_WriteReg(R4, 0x00000000)
T7474 000:706.730 - 0.018ms returns 0
T7474 000:706.749 JLINK_WriteReg(R5, 0x00000000)
T7474 000:706.768 - 0.018ms returns 0
T7474 000:706.788 JLINK_WriteReg(R6, 0x00000000)
T7474 000:706.808 - 0.019ms returns 0
T7474 000:706.827 JLINK_WriteReg(R7, 0x00000000)
T7474 000:706.845 - 0.018ms returns 0
T7474 000:706.865 JLINK_WriteReg(R8, 0x00000000)
T7474 000:706.883 - 0.018ms returns 0
T7474 000:706.903 JLINK_WriteReg(R9, 0x20200290)
T7474 000:706.921 - 0.018ms returns 0
T7474 000:706.941 JLINK_WriteReg(R10, 0x00000000)
T7474 000:706.959 - 0.018ms returns 0
T7474 000:706.978 JLINK_WriteReg(R11, 0x00000000)
T7474 000:706.997 - 0.018ms returns 0
T7474 000:707.016 JLINK_WriteReg(R12, 0x00000000)
T7474 000:707.034 - 0.018ms returns 0
T7474 000:707.054 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:707.072 - 0.018ms returns 0
T7474 000:707.092 JLINK_WriteReg(R14, 0x20200001)
T7474 000:707.110 - 0.018ms returns 0
T7474 000:707.129 JLINK_WriteReg(R15 (PC), 0x20200020)
T7474 000:707.152 - 0.022ms returns 0
T7474 000:707.171 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:707.190 - 0.018ms returns 0
T7474 000:707.209 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:707.227 - 0.018ms returns 0
T7474 000:707.247 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:707.265 - 0.018ms returns 0
T7474 000:707.284 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:707.302 - 0.018ms returns 0
T7474 000:707.322 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:707.340 - 0.018ms returns 0x00000026
T7474 000:707.360 JLINK_Go()
T7474 000:707.381   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:711.010 - 3.649ms
T7474 000:711.036 JLINK_IsHalted()
T7474 000:713.789   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:714.517 - 3.480ms returns TRUE
T7474 000:714.546 JLINK_ReadReg(R15 (PC))
T7474 000:714.566 - 0.019ms returns 0x20200000
T7474 000:714.587 JLINK_ClrBPEx(BPHandle = 0x00000026)
T7474 000:714.605 - 0.018ms returns 0x00
T7474 000:714.626 JLINK_ReadReg(R0)
T7474 000:714.645 - 0.018ms returns 0x00000001
T7474 000:714.665 JLINK_HasError()
T7474 000:714.685 JLINK_WriteReg(R0, 0x00004800)
T7474 000:714.704 - 0.018ms returns 0
T7474 000:714.724 JLINK_WriteReg(R1, 0x00000400)
T7474 000:714.743 - 0.018ms returns 0
T7474 000:714.763 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:714.782 - 0.018ms returns 0
T7474 000:714.802 JLINK_WriteReg(R3, 0x00000000)
T7474 000:714.820 - 0.018ms returns 0
T7474 000:714.841 JLINK_WriteReg(R4, 0x00000000)
T7474 000:714.859 - 0.018ms returns 0
T7474 000:714.884 JLINK_WriteReg(R5, 0x00000000)
T7474 000:714.903 - 0.018ms returns 0
T7474 000:714.923 JLINK_WriteReg(R6, 0x00000000)
T7474 000:714.942 - 0.018ms returns 0
T7474 000:714.962 JLINK_WriteReg(R7, 0x00000000)
T7474 000:714.980 - 0.018ms returns 0
T7474 000:715.001 JLINK_WriteReg(R8, 0x00000000)
T7474 000:715.019 - 0.018ms returns 0
T7474 000:715.040 JLINK_WriteReg(R9, 0x20200290)
T7474 000:715.058 - 0.018ms returns 0
T7474 000:715.079 JLINK_WriteReg(R10, 0x00000000)
T7474 000:715.097 - 0.018ms returns 0
T7474 000:715.117 JLINK_WriteReg(R11, 0x00000000)
T7474 000:715.135 - 0.018ms returns 0
T7474 000:715.158 JLINK_WriteReg(R12, 0x00000000)
T7474 000:715.178 - 0.020ms returns 0
T7474 000:715.199 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:715.218 - 0.018ms returns 0
T7474 000:715.239 JLINK_WriteReg(R14, 0x20200001)
T7474 000:715.257 - 0.018ms returns 0
T7474 000:715.278 JLINK_WriteReg(R15 (PC), 0x20200098)
T7474 000:715.296 - 0.018ms returns 0
T7474 000:715.317 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:715.335 - 0.018ms returns 0
T7474 000:715.356 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:715.374 - 0.018ms returns 0
T7474 000:715.395 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:715.413 - 0.018ms returns 0
T7474 000:715.434 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:715.452 - 0.018ms returns 0
T7474 000:715.473 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:715.492 - 0.018ms returns 0x00000027
T7474 000:715.513 JLINK_Go()
T7474 000:715.533   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:718.978 - 3.465ms
T7474 000:719.006 JLINK_IsHalted()
T7474 000:719.771 - 0.764ms returns FALSE
T7474 000:719.800 JLINK_HasError()
T7474 000:721.300 JLINK_IsHalted()
T7474 000:724.126   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:724.915 - 3.614ms returns TRUE
T7474 000:724.972 JLINK_ReadReg(R15 (PC))
T7474 000:725.016 - 0.043ms returns 0x20200000
T7474 000:725.060 JLINK_ClrBPEx(BPHandle = 0x00000027)
T7474 000:725.101 - 0.041ms returns 0x00
T7474 000:725.144 JLINK_ReadReg(R0)
T7474 000:725.184 - 0.040ms returns 0x00000000
T7474 000:725.419 JLINK_HasError()
T7474 000:725.443 JLINK_WriteReg(R0, 0x00000001)
T7474 000:725.462 - 0.019ms returns 0
T7474 000:725.482 JLINK_WriteReg(R1, 0x00000400)
T7474 000:725.500 - 0.018ms returns 0
T7474 000:725.519 JLINK_WriteReg(R2, 0x000000FF)
T7474 000:725.537 - 0.018ms returns 0
T7474 000:725.557 JLINK_WriteReg(R3, 0x00000000)
T7474 000:725.575 - 0.018ms returns 0
T7474 000:725.594 JLINK_WriteReg(R4, 0x00000000)
T7474 000:725.613 - 0.018ms returns 0
T7474 000:725.632 JLINK_WriteReg(R5, 0x00000000)
T7474 000:725.650 - 0.018ms returns 0
T7474 000:725.670 JLINK_WriteReg(R6, 0x00000000)
T7474 000:725.688 - 0.018ms returns 0
T7474 000:725.707 JLINK_WriteReg(R7, 0x00000000)
T7474 000:725.726 - 0.018ms returns 0
T7474 000:725.745 JLINK_WriteReg(R8, 0x00000000)
T7474 000:725.763 - 0.018ms returns 0
T7474 000:725.782 JLINK_WriteReg(R9, 0x20200290)
T7474 000:725.801 - 0.018ms returns 0
T7474 000:725.820 JLINK_WriteReg(R10, 0x00000000)
T7474 000:725.838 - 0.018ms returns 0
T7474 000:725.857 JLINK_WriteReg(R11, 0x00000000)
T7474 000:725.876 - 0.018ms returns 0
T7474 000:725.895 JLINK_WriteReg(R12, 0x00000000)
T7474 000:725.913 - 0.018ms returns 0
T7474 000:725.932 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:725.951 - 0.018ms returns 0
T7474 000:725.970 JLINK_WriteReg(R14, 0x20200001)
T7474 000:725.988 - 0.018ms returns 0
T7474 000:726.008 JLINK_WriteReg(R15 (PC), 0x20200094)
T7474 000:726.026 - 0.018ms returns 0
T7474 000:726.045 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:726.063 - 0.018ms returns 0
T7474 000:726.083 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:726.101 - 0.018ms returns 0
T7474 000:726.120 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:726.138 - 0.018ms returns 0
T7474 000:726.158 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:726.180 - 0.022ms returns 0
T7474 000:726.199 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:726.218 - 0.018ms returns 0x00000028
T7474 000:726.237 JLINK_Go()
T7474 000:726.258   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:729.897 - 3.659ms
T7474 000:729.954 JLINK_IsHalted()
T7474 000:732.612   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:733.392 - 3.437ms returns TRUE
T7474 000:733.418 JLINK_ReadReg(R15 (PC))
T7474 000:733.437 - 0.019ms returns 0x20200000
T7474 000:733.457 JLINK_ClrBPEx(BPHandle = 0x00000028)
T7474 000:733.475 - 0.018ms returns 0x00
T7474 000:733.494 JLINK_ReadReg(R0)
T7474 000:733.513 - 0.018ms returns 0x00000000
T7474 000:791.078 JLINK_WriteMem(0x20200000, 0x294 Bytes, ...)
T7474 000:791.106   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T7474 000:791.146   CPU_WriteMem(660 bytes @ 0x20200000)
T7474 000:794.041 - 2.961ms returns 0x294
T7474 000:794.127 JLINK_HasError()
T7474 000:794.172 JLINK_WriteReg(R0, 0x00000000)
T7474 000:794.215 - 0.042ms returns 0
T7474 000:794.257 JLINK_WriteReg(R1, 0x01F78A40)
T7474 000:794.297 - 0.040ms returns 0
T7474 000:794.339 JLINK_WriteReg(R2, 0x00000002)
T7474 000:794.379 - 0.039ms returns 0
T7474 000:794.411 JLINK_WriteReg(R3, 0x00000000)
T7474 000:794.429 - 0.018ms returns 0
T7474 000:794.449 JLINK_WriteReg(R4, 0x00000000)
T7474 000:794.467 - 0.018ms returns 0
T7474 000:794.487 JLINK_WriteReg(R5, 0x00000000)
T7474 000:794.506 - 0.018ms returns 0
T7474 000:794.525 JLINK_WriteReg(R6, 0x00000000)
T7474 000:794.544 - 0.018ms returns 0
T7474 000:794.563 JLINK_WriteReg(R7, 0x00000000)
T7474 000:794.582 - 0.018ms returns 0
T7474 000:794.602 JLINK_WriteReg(R8, 0x00000000)
T7474 000:794.628 - 0.025ms returns 0
T7474 000:794.649 JLINK_WriteReg(R9, 0x20200290)
T7474 000:794.668 - 0.018ms returns 0
T7474 000:794.689 JLINK_WriteReg(R10, 0x00000000)
T7474 000:794.710 - 0.020ms returns 0
T7474 000:794.732 JLINK_WriteReg(R11, 0x00000000)
T7474 000:794.751 - 0.018ms returns 0
T7474 000:794.772 JLINK_WriteReg(R12, 0x00000000)
T7474 000:794.791 - 0.018ms returns 0
T7474 000:794.812 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:794.831 - 0.019ms returns 0
T7474 000:794.854 JLINK_WriteReg(R14, 0x20200001)
T7474 000:794.872 - 0.018ms returns 0
T7474 000:794.893 JLINK_WriteReg(R15 (PC), 0x20200038)
T7474 000:794.912 - 0.018ms returns 0
T7474 000:794.933 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:794.952 - 0.018ms returns 0
T7474 000:794.973 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:794.992 - 0.018ms returns 0
T7474 000:795.013 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:795.031 - 0.018ms returns 0
T7474 000:795.053 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:795.071 - 0.018ms returns 0
T7474 000:795.093 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:795.114   CPU_ReadMem(4 bytes @ 0x20200000)
T7474 000:795.896   CPU_WriteMem(4 bytes @ 0x20200000)
T7474 000:796.653   CPU_ReadMem(4 bytes @ 0x20200000)
T7474 000:797.416   CPU_WriteMem(4 bytes @ 0x20200000)
T7474 000:798.184   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:798.901 - 3.808ms returns 0x00000029
T7474 000:798.932 JLINK_Go()
T7474 000:798.961   CPU_WriteMem(2 bytes @ 0x20200000)
T7474 000:799.887   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:803.474 - 4.541ms
T7474 000:803.506 JLINK_IsHalted()
T7474 000:806.244   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:807.057 - 3.550ms returns TRUE
T7474 000:807.123 JLINK_ReadReg(R15 (PC))
T7474 000:807.170 - 0.047ms returns 0x20200000
T7474 000:807.221 JLINK_ClrBPEx(BPHandle = 0x00000029)
T7474 000:807.266 - 0.044ms returns 0x00
T7474 000:807.330 JLINK_ReadReg(R0)
T7474 000:807.357 - 0.027ms returns 0x00000000
T7474 000:807.618 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:807.642   Data:  F0 4B 20 20 C1 02 00 00 C5 02 00 00 C7 02 00 00 ...
T7474 000:807.677   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:809.622 - 2.003ms returns 0x16C
T7474 000:809.683 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:809.729   Data:  7F F8 10 BD 70 29 02 D1 01 F0 BB F9 10 BD 66 29 ...
T7474 000:809.803   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:812.671 - 2.987ms returns 0x294
T7474 000:812.733 JLINK_HasError()
T7474 000:812.782 JLINK_WriteReg(R0, 0x00000000)
T7474 000:812.828 - 0.046ms returns 0
T7474 000:812.877 JLINK_WriteReg(R1, 0x00000400)
T7474 000:812.921 - 0.044ms returns 0
T7474 000:812.967 JLINK_WriteReg(R2, 0x20200294)
T7474 000:813.010 - 0.043ms returns 0
T7474 000:813.058 JLINK_WriteReg(R3, 0x00000000)
T7474 000:813.102 - 0.044ms returns 0
T7474 000:813.149 JLINK_WriteReg(R4, 0x00000000)
T7474 000:813.192 - 0.043ms returns 0
T7474 000:813.244 JLINK_WriteReg(R5, 0x00000000)
T7474 000:813.311 - 0.067ms returns 0
T7474 000:813.351 JLINK_WriteReg(R6, 0x00000000)
T7474 000:813.370 - 0.019ms returns 0
T7474 000:813.391 JLINK_WriteReg(R7, 0x00000000)
T7474 000:813.411 - 0.019ms returns 0
T7474 000:813.431 JLINK_WriteReg(R8, 0x00000000)
T7474 000:813.477 - 0.045ms returns 0
T7474 000:813.506 JLINK_WriteReg(R9, 0x20200290)
T7474 000:813.527 - 0.021ms returns 0
T7474 000:813.549 JLINK_WriteReg(R10, 0x00000000)
T7474 000:813.569 - 0.020ms returns 0
T7474 000:813.592 JLINK_WriteReg(R11, 0x00000000)
T7474 000:813.613 - 0.021ms returns 0
T7474 000:813.636 JLINK_WriteReg(R12, 0x00000000)
T7474 000:813.657 - 0.021ms returns 0
T7474 000:813.679 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:813.701 - 0.022ms returns 0
T7474 000:813.723 JLINK_WriteReg(R14, 0x20200001)
T7474 000:813.742 - 0.019ms returns 0
T7474 000:813.763 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:813.784 - 0.020ms returns 0
T7474 000:813.806 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:813.827 - 0.020ms returns 0
T7474 000:813.848 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:813.870 - 0.021ms returns 0
T7474 000:813.891 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:813.912 - 0.020ms returns 0
T7474 000:813.935 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:813.957 - 0.021ms returns 0
T7474 000:814.023 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:814.046 - 0.022ms returns 0x0000002A
T7474 000:814.067 JLINK_Go()
T7474 000:814.090   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:817.474 - 3.406ms
T7474 000:817.501 JLINK_IsHalted()
T7474 000:818.268 - 0.767ms returns FALSE
T7474 000:818.296 JLINK_HasError()
T7474 000:824.479 JLINK_IsHalted()
T7474 000:827.255   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:828.046 - 3.566ms returns TRUE
T7474 000:828.104 JLINK_ReadReg(R15 (PC))
T7474 000:828.147 - 0.042ms returns 0x20200000
T7474 000:828.189 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T7474 000:828.327 - 0.137ms returns 0x00
T7474 000:828.378 JLINK_ReadReg(R0)
T7474 000:828.419 - 0.040ms returns 0x00000000
T7474 000:829.555 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:829.586   Data:  05 D0 C9 1A DF 00 20 23 DE 1B 08 C9 0A E0 FF F7 ...
T7474 000:829.618   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:831.614 - 2.058ms returns 0x16C
T7474 000:831.670 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:831.710   Data:  01 D3 4B 01 C0 1A 52 41 03 09 8B 42 01 D3 0B 01 ...
T7474 000:831.776   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:834.732 - 3.061ms returns 0x294
T7474 000:834.793 JLINK_HasError()
T7474 000:834.842 JLINK_WriteReg(R0, 0x00000400)
T7474 000:834.887 - 0.044ms returns 0
T7474 000:834.935 JLINK_WriteReg(R1, 0x00000400)
T7474 000:834.977 - 0.042ms returns 0
T7474 000:835.024 JLINK_WriteReg(R2, 0x20200294)
T7474 000:835.066 - 0.042ms returns 0
T7474 000:835.113 JLINK_WriteReg(R3, 0x00000000)
T7474 000:835.155 - 0.042ms returns 0
T7474 000:835.202 JLINK_WriteReg(R4, 0x00000000)
T7474 000:835.245 - 0.042ms returns 0
T7474 000:835.292 JLINK_WriteReg(R5, 0x00000000)
T7474 000:835.334 - 0.041ms returns 0
T7474 000:835.380 JLINK_WriteReg(R6, 0x00000000)
T7474 000:835.410 - 0.029ms returns 0
T7474 000:835.432 JLINK_WriteReg(R7, 0x00000000)
T7474 000:835.450 - 0.018ms returns 0
T7474 000:835.472 JLINK_WriteReg(R8, 0x00000000)
T7474 000:835.491 - 0.018ms returns 0
T7474 000:835.512 JLINK_WriteReg(R9, 0x20200290)
T7474 000:835.531 - 0.018ms returns 0
T7474 000:835.552 JLINK_WriteReg(R10, 0x00000000)
T7474 000:835.571 - 0.019ms returns 0
T7474 000:835.595 JLINK_WriteReg(R11, 0x00000000)
T7474 000:835.614 - 0.018ms returns 0
T7474 000:835.635 JLINK_WriteReg(R12, 0x00000000)
T7474 000:835.654 - 0.019ms returns 0
T7474 000:835.676 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:835.695 - 0.019ms returns 0
T7474 000:835.716 JLINK_WriteReg(R14, 0x20200001)
T7474 000:835.735 - 0.018ms returns 0
T7474 000:835.756 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:835.775 - 0.019ms returns 0
T7474 000:835.796 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:835.815 - 0.019ms returns 0
T7474 000:835.837 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:835.856 - 0.018ms returns 0
T7474 000:835.877 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:835.896 - 0.019ms returns 0
T7474 000:835.917 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:835.936 - 0.018ms returns 0
T7474 000:835.958 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:835.977 - 0.019ms returns 0x0000002B
T7474 000:835.998 JLINK_Go()
T7474 000:836.019   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:839.535 - 3.536ms
T7474 000:839.564 JLINK_IsHalted()
T7474 000:840.303 - 0.738ms returns FALSE
T7474 000:840.332 JLINK_HasError()
T7474 000:841.891 JLINK_IsHalted()
T7474 000:842.638 - 0.746ms returns FALSE
T7474 000:842.665 JLINK_HasError()
T7474 000:844.317 JLINK_IsHalted()
T7474 000:845.049 - 0.730ms returns FALSE
T7474 000:845.106 JLINK_HasError()
T7474 000:846.594 JLINK_IsHalted()
T7474 000:849.384   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:850.177 - 3.583ms returns TRUE
T7474 000:850.203 JLINK_ReadReg(R15 (PC))
T7474 000:850.223 - 0.019ms returns 0x20200000
T7474 000:850.243 JLINK_ClrBPEx(BPHandle = 0x0000002B)
T7474 000:850.262 - 0.018ms returns 0x00
T7474 000:850.281 JLINK_ReadReg(R0)
T7474 000:850.302 - 0.020ms returns 0x00000000
T7474 000:850.701 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:850.724   Data:  17 46 F7 40 38 18 00 26 00 90 74 41 60 46 2F 46 ...
T7474 000:850.754   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:852.803 - 2.101ms returns 0x16C
T7474 000:852.861 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:852.904   Data:  E9 42 06 D0 19 46 A8 02 41 40 10 46 7B E7 9C 42 ...
T7474 000:852.973   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:855.926 - 3.064ms returns 0x294
T7474 000:855.953 JLINK_HasError()
T7474 000:855.973 JLINK_WriteReg(R0, 0x00000800)
T7474 000:855.993 - 0.020ms returns 0
T7474 000:856.013 JLINK_WriteReg(R1, 0x00000400)
T7474 000:856.032 - 0.018ms returns 0
T7474 000:856.052 JLINK_WriteReg(R2, 0x20200294)
T7474 000:856.071 - 0.018ms returns 0
T7474 000:856.095 JLINK_WriteReg(R3, 0x00000000)
T7474 000:856.113 - 0.018ms returns 0
T7474 000:856.133 JLINK_WriteReg(R4, 0x00000000)
T7474 000:856.152 - 0.018ms returns 0
T7474 000:856.172 JLINK_WriteReg(R5, 0x00000000)
T7474 000:856.190 - 0.018ms returns 0
T7474 000:856.210 JLINK_WriteReg(R6, 0x00000000)
T7474 000:856.229 - 0.018ms returns 0
T7474 000:856.249 JLINK_WriteReg(R7, 0x00000000)
T7474 000:856.267 - 0.018ms returns 0
T7474 000:856.287 JLINK_WriteReg(R8, 0x00000000)
T7474 000:856.306 - 0.018ms returns 0
T7474 000:856.326 JLINK_WriteReg(R9, 0x20200290)
T7474 000:856.344 - 0.018ms returns 0
T7474 000:856.364 JLINK_WriteReg(R10, 0x00000000)
T7474 000:856.383 - 0.018ms returns 0
T7474 000:856.402 JLINK_WriteReg(R11, 0x00000000)
T7474 000:856.421 - 0.018ms returns 0
T7474 000:856.445 JLINK_WriteReg(R12, 0x00000000)
T7474 000:856.463 - 0.018ms returns 0
T7474 000:856.482 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:856.501 - 0.018ms returns 0
T7474 000:856.521 JLINK_WriteReg(R14, 0x20200001)
T7474 000:856.539 - 0.018ms returns 0
T7474 000:856.558 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:856.577 - 0.018ms returns 0
T7474 000:856.596 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:856.615 - 0.018ms returns 0
T7474 000:856.634 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:856.653 - 0.018ms returns 0
T7474 000:856.672 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:856.691 - 0.018ms returns 0
T7474 000:856.710 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:856.728 - 0.018ms returns 0
T7474 000:856.748 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:856.767 - 0.019ms returns 0x0000002C
T7474 000:856.786 JLINK_Go()
T7474 000:856.807   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:860.341 - 3.554ms
T7474 000:860.368 JLINK_IsHalted()
T7474 000:861.027 - 0.658ms returns FALSE
T7474 000:861.052 JLINK_HasError()
T7474 000:864.013 JLINK_IsHalted()
T7474 000:864.764 - 0.750ms returns FALSE
T7474 000:864.791 JLINK_HasError()
T7474 000:867.011 JLINK_IsHalted()
T7474 000:869.854   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:870.515 - 3.503ms returns TRUE
T7474 000:870.541 JLINK_ReadReg(R15 (PC))
T7474 000:870.561 - 0.019ms returns 0x20200000
T7474 000:870.581 JLINK_ClrBPEx(BPHandle = 0x0000002C)
T7474 000:870.599 - 0.018ms returns 0x00
T7474 000:870.619 JLINK_ReadReg(R0)
T7474 000:870.637 - 0.018ms returns 0x00000000
T7474 000:871.040 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:871.063   Data:  8C 05 06 94 07 9C 89 0A 61 18 00 95 07 91 01 9C ...
T7474 000:871.093   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:873.013 - 1.972ms returns 0x16C
T7474 000:873.040 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:873.058   Data:  41 D0 AA 43 3F D0 30 46 0B B0 F0 BD 06 18 0D 46 ...
T7474 000:873.089   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:875.937 - 2.896ms returns 0x294
T7474 000:875.997 JLINK_HasError()
T7474 000:876.043 JLINK_WriteReg(R0, 0x00000C00)
T7474 000:876.087 - 0.044ms returns 0
T7474 000:876.132 JLINK_WriteReg(R1, 0x00000400)
T7474 000:876.174 - 0.042ms returns 0
T7474 000:876.218 JLINK_WriteReg(R2, 0x20200294)
T7474 000:876.259 - 0.041ms returns 0
T7474 000:876.303 JLINK_WriteReg(R3, 0x00000000)
T7474 000:876.348 - 0.044ms returns 0
T7474 000:876.393 JLINK_WriteReg(R4, 0x00000000)
T7474 000:876.430 - 0.037ms returns 0
T7474 000:876.453 JLINK_WriteReg(R5, 0x00000000)
T7474 000:876.472 - 0.018ms returns 0
T7474 000:876.492 JLINK_WriteReg(R6, 0x00000000)
T7474 000:876.510 - 0.018ms returns 0
T7474 000:876.529 JLINK_WriteReg(R7, 0x00000000)
T7474 000:876.548 - 0.018ms returns 0
T7474 000:876.568 JLINK_WriteReg(R8, 0x00000000)
T7474 000:876.586 - 0.018ms returns 0
T7474 000:876.606 JLINK_WriteReg(R9, 0x20200290)
T7474 000:876.625 - 0.018ms returns 0
T7474 000:876.644 JLINK_WriteReg(R10, 0x00000000)
T7474 000:876.663 - 0.018ms returns 0
T7474 000:876.682 JLINK_WriteReg(R11, 0x00000000)
T7474 000:876.707 - 0.025ms returns 0
T7474 000:876.727 JLINK_WriteReg(R12, 0x00000000)
T7474 000:876.746 - 0.018ms returns 0
T7474 000:876.765 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:876.784 - 0.018ms returns 0
T7474 000:876.804 JLINK_WriteReg(R14, 0x20200001)
T7474 000:876.822 - 0.018ms returns 0
T7474 000:876.842 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:876.860 - 0.018ms returns 0
T7474 000:876.880 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:876.899 - 0.018ms returns 0
T7474 000:876.918 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:876.937 - 0.018ms returns 0
T7474 000:876.956 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:876.975 - 0.018ms returns 0
T7474 000:876.994 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:877.013 - 0.018ms returns 0
T7474 000:877.033 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:877.052 - 0.019ms returns 0x0000002D
T7474 000:877.071 JLINK_Go()
T7474 000:877.095   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:880.470 - 3.398ms
T7474 000:880.497 JLINK_IsHalted()
T7474 000:881.141 - 0.643ms returns FALSE
T7474 000:881.166 JLINK_HasError()
T7474 000:884.358 JLINK_IsHalted()
T7474 000:885.162 - 0.802ms returns FALSE
T7474 000:885.220 JLINK_HasError()
T7474 000:886.979 JLINK_IsHalted()
T7474 000:889.865   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:890.722 - 3.743ms returns TRUE
T7474 000:890.749 JLINK_ReadReg(R15 (PC))
T7474 000:890.769 - 0.019ms returns 0x20200000
T7474 000:890.789 JLINK_ClrBPEx(BPHandle = 0x0000002D)
T7474 000:890.807 - 0.018ms returns 0x00
T7474 000:890.827 JLINK_ReadReg(R0)
T7474 000:890.845 - 0.018ms returns 0x00000000
T7474 000:891.292 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:891.316   Data:  08 9D 00 27 2C 0A 2D 06 69 18 04 9D 7C 41 2A 0A ...
T7474 000:891.346   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:893.239 - 1.946ms returns 0x16C
T7474 000:893.266 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:893.284   Data:  85 69 00 78 C0 07 07 D1 70 BD 62 68 A1 68 20 20 ...
T7474 000:893.319   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:896.162 - 2.896ms returns 0x294
T7474 000:896.189 JLINK_HasError()
T7474 000:896.209 JLINK_WriteReg(R0, 0x00001000)
T7474 000:896.229 - 0.019ms returns 0
T7474 000:896.248 JLINK_WriteReg(R1, 0x00000400)
T7474 000:896.267 - 0.018ms returns 0
T7474 000:896.286 JLINK_WriteReg(R2, 0x20200294)
T7474 000:896.305 - 0.018ms returns 0
T7474 000:896.324 JLINK_WriteReg(R3, 0x00000000)
T7474 000:896.343 - 0.018ms returns 0
T7474 000:896.362 JLINK_WriteReg(R4, 0x00000000)
T7474 000:896.381 - 0.018ms returns 0
T7474 000:896.401 JLINK_WriteReg(R5, 0x00000000)
T7474 000:896.419 - 0.018ms returns 0
T7474 000:896.439 JLINK_WriteReg(R6, 0x00000000)
T7474 000:896.457 - 0.018ms returns 0
T7474 000:896.476 JLINK_WriteReg(R7, 0x00000000)
T7474 000:896.495 - 0.018ms returns 0
T7474 000:896.519 JLINK_WriteReg(R8, 0x00000000)
T7474 000:896.537 - 0.018ms returns 0
T7474 000:896.556 JLINK_WriteReg(R9, 0x20200290)
T7474 000:896.575 - 0.018ms returns 0
T7474 000:896.594 JLINK_WriteReg(R10, 0x00000000)
T7474 000:896.613 - 0.018ms returns 0
T7474 000:896.632 JLINK_WriteReg(R11, 0x00000000)
T7474 000:896.650 - 0.018ms returns 0
T7474 000:896.670 JLINK_WriteReg(R12, 0x00000000)
T7474 000:896.688 - 0.018ms returns 0
T7474 000:896.708 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:896.764 - 0.056ms returns 0
T7474 000:896.785 JLINK_WriteReg(R14, 0x20200001)
T7474 000:896.804 - 0.018ms returns 0
T7474 000:896.823 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:896.842 - 0.018ms returns 0
T7474 000:896.861 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:896.889 - 0.027ms returns 0
T7474 000:896.908 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:896.927 - 0.018ms returns 0
T7474 000:896.951 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:896.969 - 0.018ms returns 0
T7474 000:896.988 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:897.007 - 0.018ms returns 0
T7474 000:897.027 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:897.045 - 0.019ms returns 0x0000002E
T7474 000:897.065 JLINK_Go()
T7474 000:897.086   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:900.474 - 3.409ms
T7474 000:900.501 JLINK_IsHalted()
T7474 000:901.270 - 0.769ms returns FALSE
T7474 000:901.297 JLINK_HasError()
T7474 000:904.242 JLINK_IsHalted()
T7474 000:905.012 - 0.769ms returns FALSE
T7474 000:905.038 JLINK_HasError()
T7474 000:907.250 JLINK_IsHalted()
T7474 000:909.972   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:910.659 - 3.409ms returns TRUE
T7474 000:910.685 JLINK_ReadReg(R15 (PC))
T7474 000:910.705 - 0.019ms returns 0x20200000
T7474 000:910.725 JLINK_ClrBPEx(BPHandle = 0x0000002E)
T7474 000:910.743 - 0.018ms returns 0x00
T7474 000:910.763 JLINK_ReadReg(R0)
T7474 000:910.781 - 0.018ms returns 0x00000000
T7474 000:911.175 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:911.197   Data:  70 B5 04 46 00 21 24 34 08 E0 55 07 D2 08 6D 0F ...
T7474 000:911.228   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:913.118 - 1.943ms returns 0x16C
T7474 000:913.145 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:913.168   Data:  30 3D 00 90 85 61 19 E0 02 98 BA 00 12 19 02 C8 ...
T7474 000:913.198   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:916.024 - 2.878ms returns 0x294
T7474 000:916.050 JLINK_HasError()
T7474 000:916.071 JLINK_WriteReg(R0, 0x00001400)
T7474 000:916.090 - 0.019ms returns 0
T7474 000:916.112 JLINK_WriteReg(R1, 0x00000400)
T7474 000:916.131 - 0.018ms returns 0
T7474 000:916.153 JLINK_WriteReg(R2, 0x20200294)
T7474 000:916.172 - 0.019ms returns 0
T7474 000:916.197 JLINK_WriteReg(R3, 0x00000000)
T7474 000:916.216 - 0.018ms returns 0
T7474 000:916.237 JLINK_WriteReg(R4, 0x00000000)
T7474 000:916.256 - 0.018ms returns 0
T7474 000:916.277 JLINK_WriteReg(R5, 0x00000000)
T7474 000:916.295 - 0.018ms returns 0
T7474 000:916.316 JLINK_WriteReg(R6, 0x00000000)
T7474 000:916.335 - 0.018ms returns 0
T7474 000:916.356 JLINK_WriteReg(R7, 0x00000000)
T7474 000:916.374 - 0.018ms returns 0
T7474 000:916.395 JLINK_WriteReg(R8, 0x00000000)
T7474 000:916.414 - 0.018ms returns 0
T7474 000:916.435 JLINK_WriteReg(R9, 0x20200290)
T7474 000:916.453 - 0.018ms returns 0
T7474 000:916.474 JLINK_WriteReg(R10, 0x00000000)
T7474 000:916.493 - 0.018ms returns 0
T7474 000:916.514 JLINK_WriteReg(R11, 0x00000000)
T7474 000:916.537 - 0.022ms returns 0
T7474 000:916.557 JLINK_WriteReg(R12, 0x00000000)
T7474 000:916.576 - 0.018ms returns 0
T7474 000:916.597 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:916.616 - 0.018ms returns 0
T7474 000:916.637 JLINK_WriteReg(R14, 0x20200001)
T7474 000:916.656 - 0.018ms returns 0
T7474 000:916.677 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:916.696 - 0.018ms returns 0
T7474 000:916.717 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:916.736 - 0.018ms returns 0
T7474 000:916.757 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:916.775 - 0.018ms returns 0
T7474 000:916.797 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:916.816 - 0.018ms returns 0
T7474 000:916.837 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:916.856 - 0.019ms returns 0
T7474 000:916.877 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:916.897 - 0.019ms returns 0x0000002F
T7474 000:916.918 JLINK_Go()
T7474 000:916.939   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:920.339 - 3.420ms
T7474 000:920.368 JLINK_IsHalted()
T7474 000:921.014 - 0.645ms returns FALSE
T7474 000:921.071 JLINK_HasError()
T7474 000:923.852 JLINK_IsHalted()
T7474 000:924.723 - 0.870ms returns FALSE
T7474 000:924.749 JLINK_HasError()
T7474 000:925.849 JLINK_IsHalted()
T7474 000:926.636 - 0.787ms returns FALSE
T7474 000:926.662 JLINK_HasError()
T7474 000:927.843 JLINK_IsHalted()
T7474 000:930.812   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:931.664 - 3.820ms returns TRUE
T7474 000:931.691 JLINK_ReadReg(R15 (PC))
T7474 000:931.711 - 0.019ms returns 0x20200000
T7474 000:931.730 JLINK_ClrBPEx(BPHandle = 0x0000002F)
T7474 000:931.749 - 0.018ms returns 0x00
T7474 000:931.768 JLINK_ReadReg(R0)
T7474 000:931.787 - 0.018ms returns 0x00000000
T7474 000:932.181 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:932.204   Data:  0D D0 F0 48 F0 49 28 18 48 43 07 14 18 98 00 28 ...
T7474 000:932.234   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:934.132 - 1.950ms returns 0x16C
T7474 000:934.189 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:934.230   Data:  02 F0 4E FE 02 46 0D 98 C0 0F 01 D0 2D 20 07 E0 ...
T7474 000:934.298   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:937.149 - 2.960ms returns 0x294
T7474 000:937.176 JLINK_HasError()
T7474 000:937.196 JLINK_WriteReg(R0, 0x00001800)
T7474 000:937.216 - 0.019ms returns 0
T7474 000:937.235 JLINK_WriteReg(R1, 0x00000400)
T7474 000:937.254 - 0.018ms returns 0
T7474 000:937.273 JLINK_WriteReg(R2, 0x20200294)
T7474 000:937.292 - 0.018ms returns 0
T7474 000:937.311 JLINK_WriteReg(R3, 0x00000000)
T7474 000:937.330 - 0.018ms returns 0
T7474 000:937.349 JLINK_WriteReg(R4, 0x00000000)
T7474 000:937.368 - 0.018ms returns 0
T7474 000:937.387 JLINK_WriteReg(R5, 0x00000000)
T7474 000:937.406 - 0.018ms returns 0
T7474 000:937.425 JLINK_WriteReg(R6, 0x00000000)
T7474 000:937.444 - 0.018ms returns 0
T7474 000:937.463 JLINK_WriteReg(R7, 0x00000000)
T7474 000:937.482 - 0.018ms returns 0
T7474 000:937.501 JLINK_WriteReg(R8, 0x00000000)
T7474 000:937.520 - 0.018ms returns 0
T7474 000:937.539 JLINK_WriteReg(R9, 0x20200290)
T7474 000:937.558 - 0.018ms returns 0
T7474 000:937.577 JLINK_WriteReg(R10, 0x00000000)
T7474 000:937.596 - 0.018ms returns 0
T7474 000:937.615 JLINK_WriteReg(R11, 0x00000000)
T7474 000:937.634 - 0.018ms returns 0
T7474 000:937.653 JLINK_WriteReg(R12, 0x00000000)
T7474 000:937.672 - 0.018ms returns 0
T7474 000:937.691 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:937.710 - 0.018ms returns 0
T7474 000:937.730 JLINK_WriteReg(R14, 0x20200001)
T7474 000:937.748 - 0.018ms returns 0
T7474 000:937.768 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:937.790 - 0.022ms returns 0
T7474 000:937.810 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:937.828 - 0.018ms returns 0
T7474 000:937.848 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:937.866 - 0.018ms returns 0
T7474 000:937.886 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:937.904 - 0.018ms returns 0
T7474 000:937.924 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:937.942 - 0.018ms returns 0
T7474 000:937.962 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:937.981 - 0.019ms returns 0x00000030
T7474 000:938.000 JLINK_Go()
T7474 000:938.021   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:941.484 - 3.483ms
T7474 000:941.510 JLINK_IsHalted()
T7474 000:942.266 - 0.755ms returns FALSE
T7474 000:942.293 JLINK_HasError()
T7474 000:945.986 JLINK_IsHalted()
T7474 000:946.784 - 0.798ms returns FALSE
T7474 000:946.811 JLINK_HasError()
T7474 000:947.989 JLINK_IsHalted()
T7474 000:950.871   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:951.727 - 3.738ms returns TRUE
T7474 000:951.753 JLINK_ReadReg(R15 (PC))
T7474 000:951.773 - 0.019ms returns 0x20200000
T7474 000:951.793 JLINK_ClrBPEx(BPHandle = 0x00000030)
T7474 000:951.811 - 0.018ms returns 0x00
T7474 000:951.831 JLINK_ReadReg(R0)
T7474 000:951.849 - 0.018ms returns 0x00000000
T7474 000:952.494 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:952.521   Data:  C0 0F 43 01 68 46 00 2B 03 70 01 D0 01 20 00 E0 ...
T7474 000:952.553   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:954.486 - 1.992ms returns 0x16C
T7474 000:954.516 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:954.536   Data:  40 1C 08 2A 01 DA 00 2D F4 DC 23 68 9A 06 01 D5 ...
T7474 000:954.568   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:957.428 - 2.911ms returns 0x294
T7474 000:957.455 JLINK_HasError()
T7474 000:957.475 JLINK_WriteReg(R0, 0x00001C00)
T7474 000:957.495 - 0.020ms returns 0
T7474 000:957.515 JLINK_WriteReg(R1, 0x00000400)
T7474 000:957.534 - 0.018ms returns 0
T7474 000:957.553 JLINK_WriteReg(R2, 0x20200294)
T7474 000:957.572 - 0.018ms returns 0
T7474 000:957.591 JLINK_WriteReg(R3, 0x00000000)
T7474 000:957.614 - 0.022ms returns 0
T7474 000:957.634 JLINK_WriteReg(R4, 0x00000000)
T7474 000:957.652 - 0.018ms returns 0
T7474 000:957.672 JLINK_WriteReg(R5, 0x00000000)
T7474 000:957.691 - 0.018ms returns 0
T7474 000:957.710 JLINK_WriteReg(R6, 0x00000000)
T7474 000:957.729 - 0.018ms returns 0
T7474 000:957.749 JLINK_WriteReg(R7, 0x00000000)
T7474 000:957.767 - 0.018ms returns 0
T7474 000:957.787 JLINK_WriteReg(R8, 0x00000000)
T7474 000:957.805 - 0.018ms returns 0
T7474 000:957.825 JLINK_WriteReg(R9, 0x20200290)
T7474 000:957.844 - 0.018ms returns 0
T7474 000:957.864 JLINK_WriteReg(R10, 0x00000000)
T7474 000:957.882 - 0.018ms returns 0
T7474 000:957.902 JLINK_WriteReg(R11, 0x00000000)
T7474 000:957.920 - 0.018ms returns 0
T7474 000:957.940 JLINK_WriteReg(R12, 0x00000000)
T7474 000:957.959 - 0.018ms returns 0
T7474 000:957.978 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:957.997 - 0.019ms returns 0
T7474 000:958.017 JLINK_WriteReg(R14, 0x20200001)
T7474 000:958.040 - 0.023ms returns 0
T7474 000:958.060 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:958.078 - 0.018ms returns 0
T7474 000:958.098 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:958.117 - 0.018ms returns 0
T7474 000:958.137 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:958.155 - 0.018ms returns 0
T7474 000:958.175 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:958.194 - 0.018ms returns 0
T7474 000:958.213 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:958.232 - 0.018ms returns 0
T7474 000:958.252 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:958.271 - 0.019ms returns 0x00000031
T7474 000:958.291 JLINK_Go()
T7474 000:958.312   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:961.930 - 3.639ms
T7474 000:961.957 JLINK_IsHalted()
T7474 000:962.648 - 0.691ms returns FALSE
T7474 000:962.674 JLINK_HasError()
T7474 000:965.524 JLINK_IsHalted()
T7474 000:966.290 - 0.766ms returns FALSE
T7474 000:966.348 JLINK_HasError()
T7474 000:968.099 JLINK_IsHalted()
T7474 000:971.005   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:971.779 - 3.679ms returns TRUE
T7474 000:971.806 JLINK_ReadReg(R15 (PC))
T7474 000:971.825 - 0.019ms returns 0x20200000
T7474 000:971.845 JLINK_ClrBPEx(BPHandle = 0x00000031)
T7474 000:971.863 - 0.018ms returns 0x00
T7474 000:971.883 JLINK_ReadReg(R0)
T7474 000:971.902 - 0.018ms returns 0x00000000
T7474 000:972.318 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:972.341   Data:  F8 BD 00 95 00 F0 C0 FB F8 BD 00 00 00 00 E0 FF ...
T7474 000:972.371   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:974.378 - 2.060ms returns 0x16C
T7474 000:974.407 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:974.426   Data:  20 01 C0 19 0E C8 06 91 08 93 07 92 00 68 F0 42 ...
T7474 000:974.461   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:977.285 - 2.876ms returns 0x294
T7474 000:977.347 JLINK_HasError()
T7474 000:977.395 JLINK_WriteReg(R0, 0x00002000)
T7474 000:977.439 - 0.043ms returns 0
T7474 000:977.486 JLINK_WriteReg(R1, 0x00000400)
T7474 000:977.528 - 0.041ms returns 0
T7474 000:977.574 JLINK_WriteReg(R2, 0x20200294)
T7474 000:977.615 - 0.041ms returns 0
T7474 000:977.653 JLINK_WriteReg(R3, 0x00000000)
T7474 000:977.671 - 0.018ms returns 0
T7474 000:977.692 JLINK_WriteReg(R4, 0x00000000)
T7474 000:977.711 - 0.018ms returns 0
T7474 000:977.731 JLINK_WriteReg(R5, 0x00000000)
T7474 000:977.750 - 0.018ms returns 0
T7474 000:977.771 JLINK_WriteReg(R6, 0x00000000)
T7474 000:977.790 - 0.018ms returns 0
T7474 000:977.816 JLINK_WriteReg(R7, 0x00000000)
T7474 000:977.835 - 0.018ms returns 0
T7474 000:977.856 JLINK_WriteReg(R8, 0x00000000)
T7474 000:977.874 - 0.018ms returns 0
T7474 000:977.895 JLINK_WriteReg(R9, 0x20200290)
T7474 000:977.914 - 0.018ms returns 0
T7474 000:977.935 JLINK_WriteReg(R10, 0x00000000)
T7474 000:977.954 - 0.018ms returns 0
T7474 000:977.975 JLINK_WriteReg(R11, 0x00000000)
T7474 000:977.994 - 0.018ms returns 0
T7474 000:978.015 JLINK_WriteReg(R12, 0x00000000)
T7474 000:978.033 - 0.018ms returns 0
T7474 000:978.055 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:978.073 - 0.019ms returns 0
T7474 000:978.095 JLINK_WriteReg(R14, 0x20200001)
T7474 000:978.113 - 0.018ms returns 0
T7474 000:978.134 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:978.153 - 0.018ms returns 0
T7474 000:978.174 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:978.193 - 0.018ms returns 0
T7474 000:978.214 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:978.233 - 0.018ms returns 0
T7474 000:978.254 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:978.273 - 0.018ms returns 0
T7474 000:978.294 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:978.312 - 0.018ms returns 0
T7474 000:978.334 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:978.353 - 0.019ms returns 0x00000032
T7474 000:978.374 JLINK_Go()
T7474 000:978.395   CPU_ReadMem(4 bytes @ 0x********)
T7474 000:981.869 - 3.493ms
T7474 000:981.932 JLINK_IsHalted()
T7474 000:982.722 - 0.789ms returns FALSE
T7474 000:982.751 JLINK_HasError()
T7474 000:987.896 JLINK_IsHalted()
T7474 000:990.806   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 000:991.543 - 3.646ms returns TRUE
T7474 000:991.569 JLINK_ReadReg(R15 (PC))
T7474 000:991.589 - 0.019ms returns 0x20200000
T7474 000:991.608 JLINK_ClrBPEx(BPHandle = 0x00000032)
T7474 000:991.627 - 0.018ms returns 0x00
T7474 000:991.647 JLINK_ReadReg(R0)
T7474 000:991.665 - 0.018ms returns 0x00000000
T7474 000:992.111 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 000:992.134   Data:  92 68 40 68 89 68 96 46 00 22 03 93 17 46 94 46 ...
T7474 000:992.165   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 000:994.121 - 2.009ms returns 0x16C
T7474 000:994.180 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 000:994.222   Data:  00 28 00 D0 01 20 01 99 01 9A 49 05 08 43 01 21 ...
T7474 000:994.290   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 000:997.157 - 2.977ms returns 0x294
T7474 000:997.184 JLINK_HasError()
T7474 000:997.204 JLINK_WriteReg(R0, 0x00002400)
T7474 000:997.224 - 0.020ms returns 0
T7474 000:997.244 JLINK_WriteReg(R1, 0x00000400)
T7474 000:997.262 - 0.018ms returns 0
T7474 000:997.282 JLINK_WriteReg(R2, 0x20200294)
T7474 000:997.301 - 0.018ms returns 0
T7474 000:997.320 JLINK_WriteReg(R3, 0x00000000)
T7474 000:997.339 - 0.018ms returns 0
T7474 000:997.358 JLINK_WriteReg(R4, 0x00000000)
T7474 000:997.377 - 0.018ms returns 0
T7474 000:997.401 JLINK_WriteReg(R5, 0x00000000)
T7474 000:997.419 - 0.018ms returns 0
T7474 000:997.439 JLINK_WriteReg(R6, 0x00000000)
T7474 000:997.458 - 0.018ms returns 0
T7474 000:997.477 JLINK_WriteReg(R7, 0x00000000)
T7474 000:997.496 - 0.018ms returns 0
T7474 000:997.515 JLINK_WriteReg(R8, 0x00000000)
T7474 000:997.534 - 0.018ms returns 0
T7474 000:997.554 JLINK_WriteReg(R9, 0x20200290)
T7474 000:997.572 - 0.018ms returns 0
T7474 000:997.592 JLINK_WriteReg(R10, 0x00000000)
T7474 000:997.610 - 0.018ms returns 0
T7474 000:997.630 JLINK_WriteReg(R11, 0x00000000)
T7474 000:997.649 - 0.018ms returns 0
T7474 000:997.668 JLINK_WriteReg(R12, 0x00000000)
T7474 000:997.687 - 0.018ms returns 0
T7474 000:997.706 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 000:997.725 - 0.018ms returns 0
T7474 000:997.745 JLINK_WriteReg(R14, 0x20200001)
T7474 000:997.763 - 0.018ms returns 0
T7474 000:997.783 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 000:997.801 - 0.018ms returns 0
T7474 000:997.821 JLINK_WriteReg(XPSR, 0x01000000)
T7474 000:997.840 - 0.018ms returns 0
T7474 000:997.859 JLINK_WriteReg(MSP, 0x20208000)
T7474 000:997.878 - 0.018ms returns 0
T7474 000:997.900 JLINK_WriteReg(PSP, 0x20208000)
T7474 000:997.919 - 0.018ms returns 0
T7474 000:997.938 JLINK_WriteReg(CFBP, 0x00000000)
T7474 000:997.957 - 0.018ms returns 0
T7474 000:997.977 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 000:997.996 - 0.019ms returns 0x00000033
T7474 000:998.015 JLINK_Go()
T7474 000:998.036   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:001.489 - 3.472ms
T7474 001:001.545 JLINK_IsHalted()
T7474 001:002.267 - 0.722ms returns FALSE
T7474 001:002.293 JLINK_HasError()
T7474 001:006.060 JLINK_IsHalted()
T7474 001:006.790 - 0.729ms returns FALSE
T7474 001:006.817 JLINK_HasError()
T7474 001:008.062 JLINK_IsHalted()
T7474 001:010.872   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:011.739 - 3.676ms returns TRUE
T7474 001:011.796 JLINK_ReadReg(R15 (PC))
T7474 001:011.827 - 0.031ms returns 0x20200000
T7474 001:011.847 JLINK_ClrBPEx(BPHandle = 0x00000033)
T7474 001:011.865 - 0.018ms returns 0x00
T7474 001:011.885 JLINK_ReadReg(R0)
T7474 001:011.904 - 0.018ms returns 0x00000000
T7474 001:012.305 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 001:012.328   Data:  01 20 9A 42 00 D2 40 42 70 BD 12 BA 1B BA 2D BA ...
T7474 001:012.359   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 001:014.244 - 1.938ms returns 0x16C
T7474 001:014.301 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 001:014.344   Data:  03 91 02 A9 08 70 03 98 09 78 89 00 40 18 25 21 ...
T7474 001:014.411   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 001:018.853 - 4.551ms returns 0x294
T7474 001:018.913 JLINK_HasError()
T7474 001:018.959 JLINK_WriteReg(R0, 0x00002800)
T7474 001:019.003 - 0.044ms returns 0
T7474 001:019.048 JLINK_WriteReg(R1, 0x00000400)
T7474 001:019.090 - 0.042ms returns 0
T7474 001:019.134 JLINK_WriteReg(R2, 0x20200294)
T7474 001:019.176 - 0.041ms returns 0
T7474 001:019.220 JLINK_WriteReg(R3, 0x00000000)
T7474 001:019.262 - 0.041ms returns 0
T7474 001:019.306 JLINK_WriteReg(R4, 0x00000000)
T7474 001:019.348 - 0.041ms returns 0
T7474 001:019.392 JLINK_WriteReg(R5, 0x00000000)
T7474 001:019.433 - 0.041ms returns 0
T7474 001:019.478 JLINK_WriteReg(R6, 0x00000000)
T7474 001:019.519 - 0.041ms returns 0
T7474 001:019.563 JLINK_WriteReg(R7, 0x00000000)
T7474 001:019.605 - 0.041ms returns 0
T7474 001:019.649 JLINK_WriteReg(R8, 0x00000000)
T7474 001:019.691 - 0.041ms returns 0
T7474 001:019.731 JLINK_WriteReg(R9, 0x20200290)
T7474 001:019.750 - 0.018ms returns 0
T7474 001:019.769 JLINK_WriteReg(R10, 0x00000000)
T7474 001:019.788 - 0.018ms returns 0
T7474 001:019.807 JLINK_WriteReg(R11, 0x00000000)
T7474 001:019.826 - 0.018ms returns 0
T7474 001:019.845 JLINK_WriteReg(R12, 0x00000000)
T7474 001:019.864 - 0.018ms returns 0
T7474 001:019.883 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:019.902 - 0.018ms returns 0
T7474 001:019.922 JLINK_WriteReg(R14, 0x20200001)
T7474 001:019.941 - 0.018ms returns 0
T7474 001:019.960 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 001:019.979 - 0.018ms returns 0
T7474 001:019.998 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:020.017 - 0.018ms returns 0
T7474 001:020.036 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:020.055 - 0.018ms returns 0
T7474 001:020.075 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:020.093 - 0.018ms returns 0
T7474 001:020.113 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:020.131 - 0.018ms returns 0
T7474 001:020.151 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:020.170 - 0.019ms returns 0x00000034
T7474 001:020.190 JLINK_Go()
T7474 001:020.211   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:023.761 - 3.570ms
T7474 001:023.818 JLINK_IsHalted()
T7474 001:024.518 - 0.700ms returns FALSE
T7474 001:024.544 JLINK_HasError()
T7474 001:027.065 JLINK_IsHalted()
T7474 001:027.796 - 0.731ms returns FALSE
T7474 001:027.851 JLINK_HasError()
T7474 001:029.579 JLINK_IsHalted()
T7474 001:030.291 - 0.711ms returns FALSE
T7474 001:030.349 JLINK_HasError()
T7474 001:031.587 JLINK_IsHalted()
T7474 001:034.380   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:035.194 - 3.606ms returns TRUE
T7474 001:035.258 JLINK_ReadReg(R15 (PC))
T7474 001:035.303 - 0.044ms returns 0x20200000
T7474 001:035.349 JLINK_ClrBPEx(BPHandle = 0x00000034)
T7474 001:035.389 - 0.040ms returns 0x00
T7474 001:035.434 JLINK_ReadReg(R0)
T7474 001:035.474 - 0.040ms returns 0x00000000
T7474 001:036.111 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 001:036.134   Data:  88 50 01 B0 70 47 C0 46 82 B0 01 90 00 91 00 98 ...
T7474 001:036.165   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 001:038.121 - 2.009ms returns 0x16C
T7474 001:038.153 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 001:038.171   Data:  08 4A 02 40 10 19 98 61 18 68 03 9A 10 43 18 60 ...
T7474 001:038.202   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 001:041.033 - 2.879ms returns 0x294
T7474 001:041.059 JLINK_HasError()
T7474 001:041.080 JLINK_WriteReg(R0, 0x00002C00)
T7474 001:041.099 - 0.019ms returns 0
T7474 001:041.119 JLINK_WriteReg(R1, 0x00000400)
T7474 001:041.137 - 0.018ms returns 0
T7474 001:041.162 JLINK_WriteReg(R2, 0x20200294)
T7474 001:041.181 - 0.018ms returns 0
T7474 001:041.200 JLINK_WriteReg(R3, 0x00000000)
T7474 001:041.219 - 0.018ms returns 0
T7474 001:041.238 JLINK_WriteReg(R4, 0x00000000)
T7474 001:041.256 - 0.018ms returns 0
T7474 001:041.276 JLINK_WriteReg(R5, 0x00000000)
T7474 001:041.294 - 0.018ms returns 0
T7474 001:041.314 JLINK_WriteReg(R6, 0x00000000)
T7474 001:041.332 - 0.018ms returns 0
T7474 001:041.351 JLINK_WriteReg(R7, 0x00000000)
T7474 001:041.370 - 0.018ms returns 0
T7474 001:041.389 JLINK_WriteReg(R8, 0x00000000)
T7474 001:041.408 - 0.018ms returns 0
T7474 001:041.427 JLINK_WriteReg(R9, 0x20200290)
T7474 001:041.446 - 0.018ms returns 0
T7474 001:041.465 JLINK_WriteReg(R10, 0x00000000)
T7474 001:041.483 - 0.018ms returns 0
T7474 001:041.503 JLINK_WriteReg(R11, 0x00000000)
T7474 001:041.521 - 0.018ms returns 0
T7474 001:041.541 JLINK_WriteReg(R12, 0x00000000)
T7474 001:041.559 - 0.018ms returns 0
T7474 001:041.579 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:041.597 - 0.018ms returns 0
T7474 001:041.617 JLINK_WriteReg(R14, 0x20200001)
T7474 001:041.635 - 0.018ms returns 0
T7474 001:041.654 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 001:041.673 - 0.018ms returns 0
T7474 001:041.692 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:041.711 - 0.018ms returns 0
T7474 001:041.730 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:041.748 - 0.018ms returns 0
T7474 001:041.768 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:041.790 - 0.022ms returns 0
T7474 001:041.810 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:041.828 - 0.018ms returns 0
T7474 001:041.848 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:041.867 - 0.019ms returns 0x00000035
T7474 001:041.886 JLINK_Go()
T7474 001:041.908   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:045.352 - 3.465ms
T7474 001:045.378 JLINK_IsHalted()
T7474 001:046.015 - 0.636ms returns FALSE
T7474 001:046.041 JLINK_HasError()
T7474 001:050.113 JLINK_IsHalted()
T7474 001:050.891 - 0.778ms returns FALSE
T7474 001:050.917 JLINK_HasError()
T7474 001:052.135 JLINK_IsHalted()
T7474 001:055.003   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:055.802 - 3.667ms returns TRUE
T7474 001:055.829 JLINK_ReadReg(R15 (PC))
T7474 001:055.849 - 0.019ms returns 0x20200000
T7474 001:055.869 JLINK_ClrBPEx(BPHandle = 0x00000035)
T7474 001:055.888 - 0.018ms returns 0x00
T7474 001:055.907 JLINK_ReadReg(R0)
T7474 001:055.926 - 0.018ms returns 0x00000000
T7474 001:056.319 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 001:056.342   Data:  88 88 FD FF 01 04 00 00 FF FF FF CF 40 00 FF CF ...
T7474 001:056.373   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 001:058.384 - 2.065ms returns 0x16C
T7474 001:058.411 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 001:058.430   Data:  80 18 42 68 9A 43 51 18 41 60 B0 BD 18 67 00 00 ...
T7474 001:058.461   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 001:061.286 - 2.874ms returns 0x294
T7474 001:061.315 JLINK_HasError()
T7474 001:061.336 JLINK_WriteReg(R0, 0x00003000)
T7474 001:061.355 - 0.019ms returns 0
T7474 001:061.378 JLINK_WriteReg(R1, 0x00000400)
T7474 001:061.398 - 0.019ms returns 0
T7474 001:061.419 JLINK_WriteReg(R2, 0x20200294)
T7474 001:061.438 - 0.018ms returns 0
T7474 001:061.458 JLINK_WriteReg(R3, 0x00000000)
T7474 001:061.477 - 0.018ms returns 0
T7474 001:061.498 JLINK_WriteReg(R4, 0x00000000)
T7474 001:061.516 - 0.018ms returns 0
T7474 001:061.537 JLINK_WriteReg(R5, 0x00000000)
T7474 001:061.555 - 0.018ms returns 0
T7474 001:061.576 JLINK_WriteReg(R6, 0x00000000)
T7474 001:061.595 - 0.018ms returns 0
T7474 001:061.616 JLINK_WriteReg(R7, 0x00000000)
T7474 001:061.634 - 0.018ms returns 0
T7474 001:061.655 JLINK_WriteReg(R8, 0x00000000)
T7474 001:061.673 - 0.018ms returns 0
T7474 001:061.694 JLINK_WriteReg(R9, 0x20200290)
T7474 001:061.713 - 0.018ms returns 0
T7474 001:061.734 JLINK_WriteReg(R10, 0x00000000)
T7474 001:061.752 - 0.018ms returns 0
T7474 001:061.773 JLINK_WriteReg(R11, 0x00000000)
T7474 001:061.791 - 0.018ms returns 0
T7474 001:061.816 JLINK_WriteReg(R12, 0x00000000)
T7474 001:061.835 - 0.018ms returns 0
T7474 001:061.855 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:061.874 - 0.018ms returns 0
T7474 001:061.894 JLINK_WriteReg(R14, 0x20200001)
T7474 001:061.913 - 0.018ms returns 0
T7474 001:061.934 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 001:061.953 - 0.018ms returns 0
T7474 001:061.980 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:061.999 - 0.018ms returns 0
T7474 001:062.020 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:062.038 - 0.018ms returns 0
T7474 001:062.059 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:062.078 - 0.018ms returns 0
T7474 001:062.099 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:062.118 - 0.018ms returns 0
T7474 001:062.139 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:062.158 - 0.019ms returns 0x00000036
T7474 001:062.179 JLINK_Go()
T7474 001:062.200   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:065.736 - 3.556ms
T7474 001:065.799 JLINK_IsHalted()
T7474 001:066.512 - 0.712ms returns FALSE
T7474 001:066.540 JLINK_HasError()
T7474 001:068.954 JLINK_IsHalted()
T7474 001:069.637 - 0.682ms returns FALSE
T7474 001:069.664 JLINK_HasError()
T7474 001:070.962 JLINK_IsHalted()
T7474 001:071.797 - 0.834ms returns FALSE
T7474 001:071.853 JLINK_HasError()
T7474 001:073.955 JLINK_IsHalted()
T7474 001:076.623   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:077.408 - 3.453ms returns TRUE
T7474 001:077.435 JLINK_ReadReg(R15 (PC))
T7474 001:077.454 - 0.019ms returns 0x20200000
T7474 001:077.474 JLINK_ClrBPEx(BPHandle = 0x00000036)
T7474 001:077.493 - 0.018ms returns 0x00
T7474 001:077.517 JLINK_ReadReg(R0)
T7474 001:077.535 - 0.018ms returns 0x00000000
T7474 001:077.957 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 001:077.987   Data:  48 61 05 99 00 20 00 90 88 61 05 99 08 77 05 99 ...
T7474 001:078.032   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 001:080.015 - 2.058ms returns 0x16C
T7474 001:080.042 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 001:080.061   Data:  D2 5A 52 00 80 18 00 F0 37 FF 09 9A 1F 99 89 18 ...
T7474 001:080.092   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 001:082.934 - 2.892ms returns 0x294
T7474 001:082.966 JLINK_HasError()
T7474 001:082.987 JLINK_WriteReg(R0, 0x00003400)
T7474 001:083.007 - 0.019ms returns 0
T7474 001:083.027 JLINK_WriteReg(R1, 0x00000400)
T7474 001:083.045 - 0.018ms returns 0
T7474 001:083.065 JLINK_WriteReg(R2, 0x20200294)
T7474 001:083.083 - 0.018ms returns 0
T7474 001:083.103 JLINK_WriteReg(R3, 0x00000000)
T7474 001:083.121 - 0.018ms returns 0
T7474 001:083.141 JLINK_WriteReg(R4, 0x00000000)
T7474 001:083.159 - 0.018ms returns 0
T7474 001:083.179 JLINK_WriteReg(R5, 0x00000000)
T7474 001:083.197 - 0.018ms returns 0
T7474 001:083.217 JLINK_WriteReg(R6, 0x00000000)
T7474 001:083.235 - 0.018ms returns 0
T7474 001:083.255 JLINK_WriteReg(R7, 0x00000000)
T7474 001:083.273 - 0.018ms returns 0
T7474 001:083.293 JLINK_WriteReg(R8, 0x00000000)
T7474 001:083.311 - 0.018ms returns 0
T7474 001:083.331 JLINK_WriteReg(R9, 0x20200290)
T7474 001:083.350 - 0.018ms returns 0
T7474 001:083.371 JLINK_WriteReg(R10, 0x00000000)
T7474 001:083.390 - 0.018ms returns 0
T7474 001:083.409 JLINK_WriteReg(R11, 0x00000000)
T7474 001:083.428 - 0.018ms returns 0
T7474 001:083.448 JLINK_WriteReg(R12, 0x00000000)
T7474 001:083.466 - 0.018ms returns 0
T7474 001:083.485 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:083.504 - 0.018ms returns 0
T7474 001:083.524 JLINK_WriteReg(R14, 0x20200001)
T7474 001:083.542 - 0.018ms returns 0
T7474 001:083.561 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 001:083.580 - 0.018ms returns 0
T7474 001:083.599 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:083.618 - 0.018ms returns 0
T7474 001:083.637 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:083.656 - 0.018ms returns 0
T7474 001:083.676 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:083.694 - 0.018ms returns 0
T7474 001:083.714 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:083.732 - 0.018ms returns 0
T7474 001:083.752 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:083.771 - 0.019ms returns 0x00000037
T7474 001:083.790 JLINK_Go()
T7474 001:083.811   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:087.259 - 3.468ms
T7474 001:087.286 JLINK_IsHalted()
T7474 001:088.029 - 0.741ms returns FALSE
T7474 001:088.087 JLINK_HasError()
T7474 001:090.700 JLINK_IsHalted()
T7474 001:091.393 - 0.692ms returns FALSE
T7474 001:091.419 JLINK_HasError()
T7474 001:092.703 JLINK_IsHalted()
T7474 001:093.733 - 1.030ms returns FALSE
T7474 001:093.762 JLINK_HasError()
T7474 001:096.167 JLINK_IsHalted()
T7474 001:099.031   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:099.802 - 3.635ms returns TRUE
T7474 001:099.861 JLINK_ReadReg(R15 (PC))
T7474 001:099.890 - 0.030ms returns 0x20200000
T7474 001:099.910 JLINK_ClrBPEx(BPHandle = 0x00000037)
T7474 001:099.929 - 0.018ms returns 0x00
T7474 001:099.948 JLINK_ReadReg(R0)
T7474 001:099.967 - 0.018ms returns 0x00000000
T7474 001:100.409 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 001:100.432   Data:  FF F7 F8 F9 1B 20 03 21 FF F7 0C FA 01 20 08 90 ...
T7474 001:100.463   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 001:102.380 - 1.970ms returns 0x16C
T7474 001:102.406 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 001:102.428   Data:  03 90 FF F7 87 FB 03 98 14 49 FF F7 C5 FA 03 98 ...
T7474 001:102.459   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 001:105.292 - 2.886ms returns 0x294
T7474 001:105.319 JLINK_HasError()
T7474 001:105.339 JLINK_WriteReg(R0, 0x00003800)
T7474 001:105.359 - 0.019ms returns 0
T7474 001:105.379 JLINK_WriteReg(R1, 0x00000400)
T7474 001:105.397 - 0.018ms returns 0
T7474 001:105.416 JLINK_WriteReg(R2, 0x20200294)
T7474 001:105.435 - 0.018ms returns 0
T7474 001:105.454 JLINK_WriteReg(R3, 0x00000000)
T7474 001:105.473 - 0.018ms returns 0
T7474 001:105.492 JLINK_WriteReg(R4, 0x00000000)
T7474 001:105.511 - 0.018ms returns 0
T7474 001:105.530 JLINK_WriteReg(R5, 0x00000000)
T7474 001:105.548 - 0.018ms returns 0
T7474 001:105.568 JLINK_WriteReg(R6, 0x00000000)
T7474 001:105.586 - 0.018ms returns 0
T7474 001:105.605 JLINK_WriteReg(R7, 0x00000000)
T7474 001:105.624 - 0.018ms returns 0
T7474 001:105.643 JLINK_WriteReg(R8, 0x00000000)
T7474 001:105.662 - 0.018ms returns 0
T7474 001:105.681 JLINK_WriteReg(R9, 0x20200290)
T7474 001:105.699 - 0.018ms returns 0
T7474 001:105.719 JLINK_WriteReg(R10, 0x00000000)
T7474 001:105.737 - 0.018ms returns 0
T7474 001:105.756 JLINK_WriteReg(R11, 0x00000000)
T7474 001:105.775 - 0.018ms returns 0
T7474 001:105.794 JLINK_WriteReg(R12, 0x00000000)
T7474 001:105.813 - 0.018ms returns 0
T7474 001:105.832 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:105.851 - 0.018ms returns 0
T7474 001:105.870 JLINK_WriteReg(R14, 0x20200001)
T7474 001:105.888 - 0.018ms returns 0
T7474 001:105.912 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 001:105.931 - 0.018ms returns 0
T7474 001:105.954 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:105.972 - 0.018ms returns 0
T7474 001:105.992 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:106.011 - 0.018ms returns 0
T7474 001:106.030 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:106.051 - 0.020ms returns 0
T7474 001:106.072 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:106.090 - 0.018ms returns 0
T7474 001:106.110 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:106.129 - 0.019ms returns 0x00000038
T7474 001:106.148 JLINK_Go()
T7474 001:106.178   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:109.481 - 3.332ms
T7474 001:109.509 JLINK_IsHalted()
T7474 001:110.278 - 0.769ms returns FALSE
T7474 001:110.305 JLINK_HasError()
T7474 001:113.538 JLINK_IsHalted()
T7474 001:114.273 - 0.735ms returns FALSE
T7474 001:114.301 JLINK_HasError()
T7474 001:115.540 JLINK_IsHalted()
T7474 001:118.412   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:119.173 - 3.633ms returns TRUE
T7474 001:119.203 JLINK_ReadReg(R15 (PC))
T7474 001:119.223 - 0.020ms returns 0x20200000
T7474 001:119.245 JLINK_ClrBPEx(BPHandle = 0x00000038)
T7474 001:119.264 - 0.019ms returns 0x00
T7474 001:119.286 JLINK_ReadReg(R0)
T7474 001:119.305 - 0.019ms returns 0x00000000
T7474 001:119.718 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 001:119.741   Data:  88 40 02 49 08 60 FF E7 01 B0 70 47 80 E2 00 E0 ...
T7474 001:119.772   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 001:121.756 - 2.037ms returns 0x16C
T7474 001:121.783 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 001:121.802   Data:  FF E7 FF E7 00 98 40 1C 00 90 D3 E7 05 B0 70 47 ...
T7474 001:121.833   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 001:124.734 - 2.951ms returns 0x294
T7474 001:124.761 JLINK_HasError()
T7474 001:124.782 JLINK_WriteReg(R0, 0x00003C00)
T7474 001:124.802 - 0.020ms returns 0
T7474 001:124.822 JLINK_WriteReg(R1, 0x00000400)
T7474 001:124.841 - 0.019ms returns 0
T7474 001:124.861 JLINK_WriteReg(R2, 0x20200294)
T7474 001:124.879 - 0.018ms returns 0
T7474 001:124.899 JLINK_WriteReg(R3, 0x00000000)
T7474 001:124.918 - 0.018ms returns 0
T7474 001:124.938 JLINK_WriteReg(R4, 0x00000000)
T7474 001:124.956 - 0.018ms returns 0
T7474 001:124.976 JLINK_WriteReg(R5, 0x00000000)
T7474 001:124.995 - 0.018ms returns 0
T7474 001:125.015 JLINK_WriteReg(R6, 0x00000000)
T7474 001:125.034 - 0.018ms returns 0
T7474 001:125.053 JLINK_WriteReg(R7, 0x00000000)
T7474 001:125.075 - 0.022ms returns 0
T7474 001:125.095 JLINK_WriteReg(R8, 0x00000000)
T7474 001:125.114 - 0.018ms returns 0
T7474 001:125.134 JLINK_WriteReg(R9, 0x20200290)
T7474 001:125.153 - 0.018ms returns 0
T7474 001:125.172 JLINK_WriteReg(R10, 0x00000000)
T7474 001:125.191 - 0.018ms returns 0
T7474 001:125.211 JLINK_WriteReg(R11, 0x00000000)
T7474 001:125.230 - 0.018ms returns 0
T7474 001:125.249 JLINK_WriteReg(R12, 0x00000000)
T7474 001:125.268 - 0.018ms returns 0
T7474 001:125.288 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:125.307 - 0.019ms returns 0
T7474 001:125.327 JLINK_WriteReg(R14, 0x20200001)
T7474 001:125.345 - 0.018ms returns 0
T7474 001:125.365 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 001:125.384 - 0.018ms returns 0
T7474 001:125.404 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:125.423 - 0.019ms returns 0
T7474 001:125.443 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:125.462 - 0.018ms returns 0
T7474 001:125.482 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:125.500 - 0.018ms returns 0
T7474 001:125.520 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:125.539 - 0.018ms returns 0
T7474 001:125.559 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:125.578 - 0.019ms returns 0x00000039
T7474 001:125.598 JLINK_Go()
T7474 001:125.619   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:128.988 - 3.389ms
T7474 001:129.015 JLINK_IsHalted()
T7474 001:129.765 - 0.749ms returns FALSE
T7474 001:129.792 JLINK_HasError()
T7474 001:132.276 JLINK_IsHalted()
T7474 001:133.034 - 0.756ms returns FALSE
T7474 001:133.089 JLINK_HasError()
T7474 001:134.273 JLINK_IsHalted()
T7474 001:135.037 - 0.764ms returns FALSE
T7474 001:135.064 JLINK_HasError()
T7474 001:137.991 JLINK_IsHalted()
T7474 001:140.912   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:141.654 - 3.662ms returns TRUE
T7474 001:141.681 JLINK_ReadReg(R15 (PC))
T7474 001:141.704 - 0.022ms returns 0x20200000
T7474 001:141.726 JLINK_ClrBPEx(BPHandle = 0x00000039)
T7474 001:141.745 - 0.019ms returns 0x00
T7474 001:141.765 JLINK_ReadReg(R0)
T7474 001:141.784 - 0.019ms returns 0x00000000
T7474 001:142.190 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 001:142.213   Data:  DC 00 13 19 12 59 5B 68 FC F7 74 FF FC F7 10 FF ...
T7474 001:142.244   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 001:144.255 - 2.064ms returns 0x16C
T7474 001:144.314 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 001:144.357   Data:  FF E7 15 49 01 20 08 70 12 E0 10 48 11 49 04 22 ...
T7474 001:144.426   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 001:147.294 - 2.979ms returns 0x294
T7474 001:147.356 JLINK_HasError()
T7474 001:147.403 JLINK_WriteReg(R0, 0x00004000)
T7474 001:147.445 - 0.042ms returns 0
T7474 001:147.490 JLINK_WriteReg(R1, 0x00000400)
T7474 001:147.530 - 0.040ms returns 0
T7474 001:147.575 JLINK_WriteReg(R2, 0x20200294)
T7474 001:147.615 - 0.039ms returns 0
T7474 001:147.662 JLINK_WriteReg(R3, 0x00000000)
T7474 001:147.702 - 0.040ms returns 0
T7474 001:147.748 JLINK_WriteReg(R4, 0x00000000)
T7474 001:147.787 - 0.039ms returns 0
T7474 001:147.832 JLINK_WriteReg(R5, 0x00000000)
T7474 001:147.872 - 0.039ms returns 0
T7474 001:147.917 JLINK_WriteReg(R6, 0x00000000)
T7474 001:147.957 - 0.039ms returns 0
T7474 001:148.002 JLINK_WriteReg(R7, 0x00000000)
T7474 001:148.028 - 0.027ms returns 0
T7474 001:148.049 JLINK_WriteReg(R8, 0x00000000)
T7474 001:148.068 - 0.018ms returns 0
T7474 001:148.089 JLINK_WriteReg(R9, 0x20200290)
T7474 001:148.108 - 0.018ms returns 0
T7474 001:148.129 JLINK_WriteReg(R10, 0x00000000)
T7474 001:148.147 - 0.018ms returns 0
T7474 001:148.168 JLINK_WriteReg(R11, 0x00000000)
T7474 001:148.187 - 0.018ms returns 0
T7474 001:148.208 JLINK_WriteReg(R12, 0x00000000)
T7474 001:148.227 - 0.018ms returns 0
T7474 001:148.248 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:148.267 - 0.018ms returns 0
T7474 001:148.288 JLINK_WriteReg(R14, 0x20200001)
T7474 001:148.306 - 0.018ms returns 0
T7474 001:148.327 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 001:148.346 - 0.018ms returns 0
T7474 001:148.367 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:148.385 - 0.018ms returns 0
T7474 001:148.407 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:148.425 - 0.018ms returns 0
T7474 001:148.446 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:148.465 - 0.018ms returns 0
T7474 001:148.486 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:148.504 - 0.018ms returns 0
T7474 001:148.526 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:148.545 - 0.019ms returns 0x0000003A
T7474 001:148.567 JLINK_Go()
T7474 001:148.588   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:151.981 - 3.414ms
T7474 001:152.010 JLINK_IsHalted()
T7474 001:152.855 - 0.844ms returns FALSE
T7474 001:152.885 JLINK_HasError()
T7474 001:156.274 JLINK_IsHalted()
T7474 001:157.023 - 0.748ms returns FALSE
T7474 001:157.050 JLINK_HasError()
T7474 001:158.291 JLINK_IsHalted()
T7474 001:161.135   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:161.936 - 3.644ms returns TRUE
T7474 001:162.001 JLINK_ReadReg(R15 (PC))
T7474 001:162.044 - 0.042ms returns 0x20200000
T7474 001:162.086 JLINK_ClrBPEx(BPHandle = 0x0000003A)
T7474 001:162.119 - 0.033ms returns 0x00
T7474 001:162.139 JLINK_ReadReg(R0)
T7474 001:162.158 - 0.018ms returns 0x00000000
T7474 001:162.552 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 001:162.575   Data:  8B 42 27 D3 09 02 FF 22 12 BA 03 0C 8B 42 02 D3 ...
T7474 001:162.606   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 001:164.502 - 1.949ms returns 0x16C
T7474 001:164.560 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 001:164.602   Data:  52 41 43 09 8B 42 01 D3 4B 01 C0 1A 52 41 03 09 ...
T7474 001:164.671   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 001:167.527 - 2.967ms returns 0x294
T7474 001:167.553 JLINK_HasError()
T7474 001:167.573 JLINK_WriteReg(R0, 0x00004400)
T7474 001:167.593 - 0.019ms returns 0
T7474 001:167.613 JLINK_WriteReg(R1, 0x00000400)
T7474 001:167.633 - 0.020ms returns 0
T7474 001:167.653 JLINK_WriteReg(R2, 0x20200294)
T7474 001:167.672 - 0.018ms returns 0
T7474 001:167.691 JLINK_WriteReg(R3, 0x00000000)
T7474 001:167.710 - 0.018ms returns 0
T7474 001:167.729 JLINK_WriteReg(R4, 0x00000000)
T7474 001:167.747 - 0.018ms returns 0
T7474 001:167.767 JLINK_WriteReg(R5, 0x00000000)
T7474 001:167.785 - 0.018ms returns 0
T7474 001:167.805 JLINK_WriteReg(R6, 0x00000000)
T7474 001:167.823 - 0.018ms returns 0
T7474 001:167.842 JLINK_WriteReg(R7, 0x00000000)
T7474 001:167.861 - 0.018ms returns 0
T7474 001:167.880 JLINK_WriteReg(R8, 0x00000000)
T7474 001:167.899 - 0.018ms returns 0
T7474 001:167.918 JLINK_WriteReg(R9, 0x20200290)
T7474 001:167.936 - 0.018ms returns 0
T7474 001:167.956 JLINK_WriteReg(R10, 0x00000000)
T7474 001:167.974 - 0.018ms returns 0
T7474 001:167.994 JLINK_WriteReg(R11, 0x00000000)
T7474 001:168.036 - 0.041ms returns 0
T7474 001:168.055 JLINK_WriteReg(R12, 0x00000000)
T7474 001:168.074 - 0.018ms returns 0
T7474 001:168.093 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:168.112 - 0.018ms returns 0
T7474 001:168.132 JLINK_WriteReg(R14, 0x20200001)
T7474 001:168.154 - 0.022ms returns 0
T7474 001:168.174 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 001:168.193 - 0.018ms returns 0
T7474 001:168.212 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:168.231 - 0.018ms returns 0
T7474 001:168.251 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:168.269 - 0.018ms returns 0
T7474 001:168.289 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:168.307 - 0.018ms returns 0
T7474 001:168.327 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:168.345 - 0.018ms returns 0
T7474 001:168.365 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:168.384 - 0.019ms returns 0x0000003B
T7474 001:168.404 JLINK_Go()
T7474 001:168.425   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:172.007 - 3.602ms
T7474 001:172.064 JLINK_IsHalted()
T7474 001:172.771 - 0.706ms returns FALSE
T7474 001:172.797 JLINK_HasError()
T7474 001:175.003 JLINK_IsHalted()
T7474 001:175.800 - 0.796ms returns FALSE
T7474 001:175.858 JLINK_HasError()
T7474 001:178.996 JLINK_IsHalted()
T7474 001:181.857   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:182.527 - 3.530ms returns TRUE
T7474 001:182.552 JLINK_ReadReg(R15 (PC))
T7474 001:182.572 - 0.019ms returns 0x20200000
T7474 001:182.592 JLINK_ClrBPEx(BPHandle = 0x0000003B)
T7474 001:182.616 - 0.023ms returns 0x00
T7474 001:182.635 JLINK_ReadReg(R0)
T7474 001:182.654 - 0.018ms returns 0x00000000
T7474 001:183.285 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
T7474 001:183.308   Data:  33 34 35 36 37 38 39 61 62 63 64 65 66 78 70 00 ...
T7474 001:183.339   CPU_WriteMem(364 bytes @ 0x20200294)
T7474 001:185.256 - 1.969ms returns 0x16C
T7474 001:185.313 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
T7474 001:185.354   Data:  2D 25 64 2D 25 64 2D 25 64 2D 25 64 0D 0A 00 00 ...
T7474 001:185.420   CPU_WriteMem(660 bytes @ 0x20200400)
T7474 001:188.291 - 2.977ms returns 0x294
T7474 001:188.319 JLINK_HasError()
T7474 001:188.339 JLINK_WriteReg(R0, 0x00004800)
T7474 001:188.359 - 0.020ms returns 0
T7474 001:188.379 JLINK_WriteReg(R1, 0x00000348)
T7474 001:188.398 - 0.018ms returns 0
T7474 001:188.418 JLINK_WriteReg(R2, 0x20200294)
T7474 001:188.436 - 0.018ms returns 0
T7474 001:188.456 JLINK_WriteReg(R3, 0x00000000)
T7474 001:188.474 - 0.018ms returns 0
T7474 001:188.494 JLINK_WriteReg(R4, 0x00000000)
T7474 001:188.513 - 0.018ms returns 0
T7474 001:188.532 JLINK_WriteReg(R5, 0x00000000)
T7474 001:188.551 - 0.018ms returns 0
T7474 001:188.571 JLINK_WriteReg(R6, 0x00000000)
T7474 001:188.589 - 0.018ms returns 0
T7474 001:188.609 JLINK_WriteReg(R7, 0x00000000)
T7474 001:188.627 - 0.018ms returns 0
T7474 001:188.647 JLINK_WriteReg(R8, 0x00000000)
T7474 001:188.665 - 0.018ms returns 0
T7474 001:188.685 JLINK_WriteReg(R9, 0x20200290)
T7474 001:188.704 - 0.018ms returns 0
T7474 001:188.724 JLINK_WriteReg(R10, 0x00000000)
T7474 001:188.743 - 0.018ms returns 0
T7474 001:188.762 JLINK_WriteReg(R11, 0x00000000)
T7474 001:188.783 - 0.021ms returns 0
T7474 001:188.804 JLINK_WriteReg(R12, 0x00000000)
T7474 001:188.823 - 0.019ms returns 0
T7474 001:188.843 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:188.862 - 0.019ms returns 0
T7474 001:188.881 JLINK_WriteReg(R14, 0x20200001)
T7474 001:188.900 - 0.018ms returns 0
T7474 001:188.920 JLINK_WriteReg(R15 (PC), 0x20200120)
T7474 001:188.938 - 0.018ms returns 0
T7474 001:188.958 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:188.977 - 0.018ms returns 0
T7474 001:188.996 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:189.015 - 0.018ms returns 0
T7474 001:189.034 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:189.057 - 0.022ms returns 0
T7474 001:189.076 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:189.095 - 0.018ms returns 0
T7474 001:189.114 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:189.133 - 0.018ms returns 0x0000003C
T7474 001:189.152 JLINK_Go()
T7474 001:189.174   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:192.728 - 3.575ms
T7474 001:192.755 JLINK_IsHalted()
T7474 001:193.522 - 0.766ms returns FALSE
T7474 001:193.549 JLINK_HasError()
T7474 001:196.481 JLINK_IsHalted()
T7474 001:197.318 - 0.836ms returns FALSE
T7474 001:197.376 JLINK_HasError()
T7474 001:199.308 JLINK_IsHalted()
T7474 001:202.137   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:202.947 - 3.638ms returns TRUE
T7474 001:203.004 JLINK_ReadReg(R15 (PC))
T7474 001:203.047 - 0.042ms returns 0x20200000
T7474 001:203.089 JLINK_ClrBPEx(BPHandle = 0x0000003C)
T7474 001:203.130 - 0.040ms returns 0x00
T7474 001:203.171 JLINK_ReadReg(R0)
T7474 001:203.195 - 0.024ms returns 0x00000000
T7474 001:203.216 JLINK_HasError()
T7474 001:203.236 JLINK_WriteReg(R0, 0x00000002)
T7474 001:203.260 - 0.024ms returns 0
T7474 001:203.280 JLINK_WriteReg(R1, 0x00000348)
T7474 001:203.299 - 0.018ms returns 0
T7474 001:203.318 JLINK_WriteReg(R2, 0x20200294)
T7474 001:203.337 - 0.018ms returns 0
T7474 001:203.360 JLINK_WriteReg(R3, 0x00000000)
T7474 001:203.379 - 0.018ms returns 0
T7474 001:203.400 JLINK_WriteReg(R4, 0x00000000)
T7474 001:203.419 - 0.019ms returns 0
T7474 001:203.440 JLINK_WriteReg(R5, 0x00000000)
T7474 001:203.459 - 0.018ms returns 0
T7474 001:203.481 JLINK_WriteReg(R6, 0x00000000)
T7474 001:203.500 - 0.018ms returns 0
T7474 001:203.521 JLINK_WriteReg(R7, 0x00000000)
T7474 001:203.540 - 0.018ms returns 0
T7474 001:203.561 JLINK_WriteReg(R8, 0x00000000)
T7474 001:203.580 - 0.018ms returns 0
T7474 001:203.601 JLINK_WriteReg(R9, 0x20200290)
T7474 001:203.620 - 0.018ms returns 0
T7474 001:203.641 JLINK_WriteReg(R10, 0x00000000)
T7474 001:203.660 - 0.019ms returns 0
T7474 001:203.681 JLINK_WriteReg(R11, 0x00000000)
T7474 001:203.701 - 0.019ms returns 0
T7474 001:203.722 JLINK_WriteReg(R12, 0x00000000)
T7474 001:203.742 - 0.019ms returns 0
T7474 001:203.765 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:203.785 - 0.020ms returns 0
T7474 001:203.807 JLINK_WriteReg(R14, 0x20200001)
T7474 001:203.826 - 0.019ms returns 0
T7474 001:203.848 JLINK_WriteReg(R15 (PC), 0x20200094)
T7474 001:203.868 - 0.019ms returns 0
T7474 001:203.889 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:203.908 - 0.019ms returns 0
T7474 001:203.930 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:203.948 - 0.018ms returns 0
T7474 001:203.970 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:203.989 - 0.018ms returns 0
T7474 001:204.010 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:204.029 - 0.019ms returns 0
T7474 001:204.051 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:204.070 - 0.019ms returns 0x0000003D
T7474 001:204.091 JLINK_Go()
T7474 001:204.116   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:207.595 - 3.503ms
T7474 001:207.624 JLINK_IsHalted()
T7474 001:210.405   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:211.177 - 3.551ms returns TRUE
T7474 001:211.218 JLINK_ReadReg(R15 (PC))
T7474 001:211.238 - 0.019ms returns 0x20200000
T7474 001:211.259 JLINK_ClrBPEx(BPHandle = 0x0000003D)
T7474 001:211.278 - 0.018ms returns 0x00
T7474 001:211.299 JLINK_ReadReg(R0)
T7474 001:211.317 - 0.018ms returns 0x00000000
T7474 001:269.016 JLINK_WriteMem(0x20200000, 0x294 Bytes, ...)
T7474 001:269.040   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T7474 001:269.074   CPU_WriteMem(660 bytes @ 0x20200000)
T7474 001:271.936 - 2.920ms returns 0x294
T7474 001:271.980 JLINK_HasError()
T7474 001:272.001 JLINK_WriteReg(R0, 0x00000000)
T7474 001:272.021 - 0.020ms returns 0
T7474 001:272.041 JLINK_WriteReg(R1, 0x01F78A40)
T7474 001:272.059 - 0.018ms returns 0
T7474 001:272.083 JLINK_WriteReg(R2, 0x00000003)
T7474 001:272.102 - 0.018ms returns 0
T7474 001:272.121 JLINK_WriteReg(R3, 0x00000000)
T7474 001:272.140 - 0.018ms returns 0
T7474 001:272.160 JLINK_WriteReg(R4, 0x00000000)
T7474 001:272.178 - 0.018ms returns 0
T7474 001:272.198 JLINK_WriteReg(R5, 0x00000000)
T7474 001:272.216 - 0.018ms returns 0
T7474 001:272.236 JLINK_WriteReg(R6, 0x00000000)
T7474 001:272.255 - 0.018ms returns 0
T7474 001:272.274 JLINK_WriteReg(R7, 0x00000000)
T7474 001:272.293 - 0.018ms returns 0
T7474 001:272.312 JLINK_WriteReg(R8, 0x00000000)
T7474 001:272.330 - 0.018ms returns 0
T7474 001:272.350 JLINK_WriteReg(R9, 0x20200290)
T7474 001:272.368 - 0.018ms returns 0
T7474 001:272.388 JLINK_WriteReg(R10, 0x00000000)
T7474 001:272.407 - 0.018ms returns 0
T7474 001:272.426 JLINK_WriteReg(R11, 0x00000000)
T7474 001:272.445 - 0.018ms returns 0
T7474 001:272.486 JLINK_WriteReg(R12, 0x00000000)
T7474 001:272.504 - 0.018ms returns 0
T7474 001:272.524 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:272.543 - 0.019ms returns 0
T7474 001:272.562 JLINK_WriteReg(R14, 0x20200001)
T7474 001:272.581 - 0.018ms returns 0
T7474 001:272.600 JLINK_WriteReg(R15 (PC), 0x20200038)
T7474 001:272.619 - 0.018ms returns 0
T7474 001:272.638 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:272.657 - 0.018ms returns 0
T7474 001:272.676 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:272.695 - 0.018ms returns 0
T7474 001:272.714 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:272.733 - 0.018ms returns 0
T7474 001:272.752 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:272.771 - 0.018ms returns 0
T7474 001:272.791 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:272.812   CPU_ReadMem(4 bytes @ 0x20200000)
T7474 001:273.532   CPU_WriteMem(4 bytes @ 0x20200000)
T7474 001:274.297   CPU_ReadMem(4 bytes @ 0x20200000)
T7474 001:275.035   CPU_WriteMem(4 bytes @ 0x20200000)
T7474 001:275.795   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:276.533 - 3.741ms returns 0x0000003E
T7474 001:276.559 JLINK_Go()
T7474 001:276.579   CPU_WriteMem(2 bytes @ 0x20200000)
T7474 001:277.290   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:280.734 - 4.175ms
T7474 001:280.761 JLINK_IsHalted()
T7474 001:283.536   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:284.309 - 3.546ms returns TRUE
T7474 001:284.349 JLINK_ReadReg(R15 (PC))
T7474 001:284.369 - 0.019ms returns 0x20200000
T7474 001:284.389 JLINK_ClrBPEx(BPHandle = 0x0000003E)
T7474 001:284.408 - 0.018ms returns 0x00
T7474 001:284.428 JLINK_ReadReg(R0)
T7474 001:284.451 - 0.023ms returns 0x00000000
T7474 001:284.471 JLINK_HasError()
T7474 001:284.491 JLINK_WriteReg(R0, 0xFFFFFFFF)
T7474 001:284.510 - 0.019ms returns 0
T7474 001:284.530 JLINK_WriteReg(R1, 0x00000000)
T7474 001:284.548 - 0.018ms returns 0
T7474 001:284.568 JLINK_WriteReg(R2, 0x00004B48)
T7474 001:284.587 - 0.018ms returns 0
T7474 001:284.606 JLINK_WriteReg(R3, 0x04C11DB7)
T7474 001:284.625 - 0.018ms returns 0
T7474 001:284.644 JLINK_WriteReg(R4, 0x00000000)
T7474 001:284.662 - 0.018ms returns 0
T7474 001:284.682 JLINK_WriteReg(R5, 0x00000000)
T7474 001:284.700 - 0.018ms returns 0
T7474 001:284.720 JLINK_WriteReg(R6, 0x00000000)
T7474 001:284.738 - 0.018ms returns 0
T7474 001:284.758 JLINK_WriteReg(R7, 0x00000000)
T7474 001:284.776 - 0.018ms returns 0
T7474 001:284.796 JLINK_WriteReg(R8, 0x00000000)
T7474 001:284.814 - 0.018ms returns 0
T7474 001:284.834 JLINK_WriteReg(R9, 0x20200290)
T7474 001:284.852 - 0.018ms returns 0
T7474 001:284.872 JLINK_WriteReg(R10, 0x00000000)
T7474 001:284.890 - 0.018ms returns 0
T7474 001:284.912 JLINK_WriteReg(R11, 0x00000000)
T7474 001:284.932 - 0.019ms returns 0
T7474 001:284.952 JLINK_WriteReg(R12, 0x00000000)
T7474 001:284.970 - 0.018ms returns 0
T7474 001:284.990 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:285.008 - 0.018ms returns 0
T7474 001:285.028 JLINK_WriteReg(R14, 0x20200001)
T7474 001:285.046 - 0.018ms returns 0
T7474 001:285.066 JLINK_WriteReg(R15 (PC), 0x20200002)
T7474 001:285.084 - 0.018ms returns 0
T7474 001:285.104 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:285.122 - 0.018ms returns 0
T7474 001:285.142 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:285.160 - 0.018ms returns 0
T7474 001:285.179 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:285.198 - 0.018ms returns 0
T7474 001:285.217 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:285.236 - 0.018ms returns 0
T7474 001:285.256 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:285.278 - 0.022ms returns 0x0000003F
T7474 001:285.298 JLINK_Go()
T7474 001:285.318   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:288.732 - 3.433ms
T7474 001:288.758 JLINK_IsHalted()
T7474 001:289.411 - 0.651ms returns FALSE
T7474 001:289.474 JLINK_HasError()
T7474 001:293.117 JLINK_IsHalted()
T7474 001:293.921 - 0.803ms returns FALSE
T7474 001:293.981 JLINK_HasError()
T7474 001:295.148 JLINK_IsHalted()
T7474 001:295.899 - 0.750ms returns FALSE
T7474 001:295.926 JLINK_HasError()
T7474 001:302.371 JLINK_IsHalted()
T7474 001:303.173 - 0.801ms returns FALSE
T7474 001:303.202 JLINK_HasError()
T7474 001:305.388 JLINK_IsHalted()
T7474 001:306.188 - 0.800ms returns FALSE
T7474 001:306.215 JLINK_HasError()
T7474 001:307.990 JLINK_IsHalted()
T7474 001:308.790 - 0.799ms returns FALSE
T7474 001:308.829 JLINK_HasError()
T7474 001:309.982 JLINK_IsHalted()
T7474 001:310.650 - 0.667ms returns FALSE
T7474 001:310.676 JLINK_HasError()
T7474 001:311.991 JLINK_IsHalted()
T7474 001:312.791 - 0.800ms returns FALSE
T7474 001:312.818 JLINK_HasError()
T7474 001:315.999 JLINK_IsHalted()
T7474 001:316.776 - 0.777ms returns FALSE
T7474 001:316.804 JLINK_HasError()
T7474 001:317.982 JLINK_IsHalted()
T7474 001:318.790 - 0.807ms returns FALSE
T7474 001:318.829 JLINK_HasError()
T7474 001:319.981 JLINK_IsHalted()
T7474 001:320.651 - 0.669ms returns FALSE
T7474 001:320.677 JLINK_HasError()
T7474 001:322.989 JLINK_IsHalted()
T7474 001:325.903   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:326.657 - 3.667ms returns TRUE
T7474 001:326.683 JLINK_ReadReg(R15 (PC))
T7474 001:326.704 - 0.020ms returns 0x20200000
T7474 001:326.723 JLINK_ClrBPEx(BPHandle = 0x0000003F)
T7474 001:326.742 - 0.018ms returns 0x00
T7474 001:326.762 JLINK_ReadReg(R0)
T7474 001:326.781 - 0.018ms returns 0xE62B862A
T7474 001:327.627 JLINK_HasError()
T7474 001:327.652 JLINK_WriteReg(R0, 0x00000003)
T7474 001:327.672 - 0.019ms returns 0
T7474 001:327.692 JLINK_WriteReg(R1, 0x00000000)
T7474 001:327.711 - 0.018ms returns 0
T7474 001:327.730 JLINK_WriteReg(R2, 0x00004B48)
T7474 001:327.749 - 0.019ms returns 0
T7474 001:327.769 JLINK_WriteReg(R3, 0x04C11DB7)
T7474 001:327.788 - 0.018ms returns 0
T7474 001:327.808 JLINK_WriteReg(R4, 0x00000000)
T7474 001:327.826 - 0.018ms returns 0
T7474 001:327.846 JLINK_WriteReg(R5, 0x00000000)
T7474 001:327.865 - 0.018ms returns 0
T7474 001:327.885 JLINK_WriteReg(R6, 0x00000000)
T7474 001:327.903 - 0.018ms returns 0
T7474 001:327.923 JLINK_WriteReg(R7, 0x00000000)
T7474 001:327.941 - 0.018ms returns 0
T7474 001:327.961 JLINK_WriteReg(R8, 0x00000000)
T7474 001:327.980 - 0.018ms returns 0
T7474 001:328.000 JLINK_WriteReg(R9, 0x20200290)
T7474 001:328.018 - 0.018ms returns 0
T7474 001:328.038 JLINK_WriteReg(R10, 0x00000000)
T7474 001:328.057 - 0.018ms returns 0
T7474 001:328.076 JLINK_WriteReg(R11, 0x00000000)
T7474 001:328.095 - 0.018ms returns 0
T7474 001:328.115 JLINK_WriteReg(R12, 0x00000000)
T7474 001:328.133 - 0.018ms returns 0
T7474 001:328.153 JLINK_WriteReg(R13 (SP), 0x20208000)
T7474 001:328.172 - 0.019ms returns 0
T7474 001:328.192 JLINK_WriteReg(R14, 0x20200001)
T7474 001:328.210 - 0.018ms returns 0
T7474 001:328.232 JLINK_WriteReg(R15 (PC), 0x20200094)
T7474 001:328.252 - 0.020ms returns 0
T7474 001:328.272 JLINK_WriteReg(XPSR, 0x01000000)
T7474 001:328.290 - 0.018ms returns 0
T7474 001:328.310 JLINK_WriteReg(MSP, 0x20208000)
T7474 001:328.329 - 0.018ms returns 0
T7474 001:328.349 JLINK_WriteReg(PSP, 0x20208000)
T7474 001:328.371 - 0.022ms returns 0
T7474 001:328.399 JLINK_WriteReg(CFBP, 0x00000000)
T7474 001:328.423 - 0.025ms returns 0
T7474 001:328.446 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
T7474 001:328.472 - 0.025ms returns 0x00000040
T7474 001:328.492 JLINK_Go()
T7474 001:328.521   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:332.010 - 3.517ms
T7474 001:332.069 JLINK_IsHalted()
T7474 001:334.822   CPU_ReadMem(2 bytes @ 0x20200000)
T7474 001:335.655 - 3.586ms returns TRUE
T7474 001:335.681 JLINK_ReadReg(R15 (PC))
T7474 001:335.701 - 0.019ms returns 0x20200000
T7474 001:335.721 JLINK_ClrBPEx(BPHandle = 0x00000040)
T7474 001:335.740 - 0.018ms returns 0x00
T7474 001:335.759 JLINK_ReadReg(R0)
T7474 001:335.778 - 0.018ms returns 0x00000000
T7474 001:389.131 JLINK_WriteMemEx(0x20200000, 0x00000002 Bytes, Flags = 0x02000000)
T7474 001:389.158   Data:  FE E7
T7474 001:389.191   CPU_WriteMem(2 bytes @ 0x20200000)
T7474 001:389.942 - 0.810ms returns 0x2
T7474 001:390.000 JLINK_HasError()
T7474 001:390.043 JLINK_HasError()
T7474 001:390.086 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T7474 001:390.126 - 0.040ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T7474 001:390.168 JLINK_Reset()
T7474 001:390.212   CPU_ReadMem(4 bytes @ 0x20200000)
T7474 001:391.028   CPU_WriteMem(4 bytes @ 0x20200000)
T7474 001:393.869   Memory map 'before startup completion point' is active
T7474 001:393.906   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T7474 001:394.734   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7474 001:397.445   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T7474 001:400.835   Reset: Reset device via AIRCR.SYSRESETREQ.
T7474 001:400.871   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T7474 001:455.955   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7474 001:456.659   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7474 001:457.407   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7474 001:464.739   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7474 001:468.298   CPU_WriteMem(4 bytes @ 0x********)
T7474 001:469.056   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T7474 001:469.813   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:470.743   CPU_WriteMem(4 bytes @ 0x********)
T7474 001:471.418 - 81.249ms
T7474 001:471.451 JLINK_Go()
T7474 001:471.477   CPU_ReadMem(4 bytes @ 0x********)
T7474 001:472.164   CPU_WriteMem(4 bytes @ 0x********)
T7474 001:472.925   CPU_WriteMem(4 bytes @ 0xE0002008)
T7474 001:472.958   CPU_WriteMem(4 bytes @ 0xE000200C)
T7474 001:472.986   CPU_WriteMem(4 bytes @ 0xE0002010)
T7474 001:473.014   CPU_WriteMem(4 bytes @ 0xE0002014)
T7474 001:474.200   CPU_WriteMem(4 bytes @ 0xE0001004)
T7474 001:478.423   Memory map 'after startup completion point' is active
T7474 001:478.457 - 7.005ms
T7474 001:484.253 JLINK_Close()
T7474 001:484.957   CPU is running
T7474 001:485.033   CPU_WriteMem(4 bytes @ 0xE0002008)
T7474 001:485.811   CPU is running
T7474 001:485.845   CPU_WriteMem(4 bytes @ 0xE000200C)
T7474 001:486.543   CPU is running
T7474 001:486.577   CPU_WriteMem(4 bytes @ 0xE0002010)
T7474 001:487.291   CPU is running
T7474 001:487.325   CPU_WriteMem(4 bytes @ 0xE0002014)
T7474 001:507.870 - 23.616ms
T7474 001:507.902   
T7474 001:507.927   Closed
