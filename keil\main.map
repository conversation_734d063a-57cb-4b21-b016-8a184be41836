Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to scheduler.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to encoder_driver.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_driver.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) for SYSCFG_DL_MOTOR_PWM_LEFT_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) for SYSCFG_DL_MOTOR_PWM_RIGHT_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) for SYSCFG_DL_ADC_VOLTAGE_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for SYSCFG_DL_SYSCTL_CLK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gMOTOR_PWM_RIGHTBackup) for gMOTOR_PWM_RIGHTBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for DL_ADC12_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for DL_ADC12_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for DL_GPIO_initPeripheralAnalogFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for DL_GPIO_initDigitalInputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for DL_GPIO_setLowerPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for DL_SYSCTL_setFlashWaitState
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for DL_SYSCTL_setULPCLKDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for DL_SYSCTL_enableMFCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK) for DL_SYSCTL_enableMFPCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource) for DL_SYSCTL_setMFPCLKSource
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for DL_ADC12_initSingleSample
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for DL_ADC12_configConversionMem
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_setPowerDownMode) for DL_ADC12_setPowerDownMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for DL_ADC12_setSampleTime0
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for DL_ADC12_enableConversions
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to ti_msp_dl_config.o(.rodata.gADC_VOLTAGEClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SysTick_Config) for SysTick_Config
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_getClockStatus) for DL_SYSCTL_getClockStatus
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gMOTOR_PWM_RIGHTBackup) for gMOTOR_PWM_RIGHTBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gMOTOR_PWM_RIGHTBackup) for gMOTOR_PWM_RIGHTBackup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset) refers to ti_msp_dl_config.o(.text.DL_ADC12_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower) refers to ti_msp_dl_config.o(.text.DL_ADC12_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFPCLK) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setMFPCLKSource) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_getClockStatus) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_getClockStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample) refers to ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem) refers to ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_ADC12_setPowerDownMode) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setPowerDownMode) refers to ti_msp_dl_config.o(.text.DL_ADC12_setPowerDownMode) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0) refers to ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions) refers to ti_msp_dl_config.o(.text.DL_ADC12_enableConversions) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SysTick_Config) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.ARM.exidx.text.SysTick_Config) refers to ti_msp_dl_config.o(.text.SysTick_Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    main.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    main.o(.text.main) refers to main.o(.text.user_config) for user_config
    main.o(.text.main) refers to scheduler.o(.text.scheduler_init) for scheduler_init
    main.o(.text.main) refers to scheduler.o(.text.scheduler_run) for scheduler_run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.user_config) refers to main.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    main.o(.text.user_config) refers to main.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    main.o(.ARM.exidx.text.user_config) refers to main.o(.text.user_config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to main.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to main.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    uart_driver.o(.text.uart_send_char) refers to uart_driver.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    uart_driver.o(.text.uart_send_char) refers to uart_driver.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    uart_driver.o(.ARM.exidx.text.uart_send_char) refers to uart_driver.o(.text.uart_send_char) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.DL_UART_isBusy) refers to uart_driver.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.DL_UART_transmitData) refers to uart_driver.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    uart_driver.o(.text.uart_send_string) refers to uart_driver.o(.text.uart_send_char) for uart_send_char
    uart_driver.o(.ARM.exidx.text.uart_send_string) refers to uart_driver.o(.text.uart_send_string) for [Anonymous Symbol]
    uart_driver.o(.text.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(.text.my_printf) refers to uart_driver.o(.text.uart_send_string) for uart_send_string
    uart_driver.o(.ARM.exidx.text.my_printf) refers to uart_driver.o(.text.my_printf) for [Anonymous Symbol]
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_rx_index) for uart_rx_index
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_rx_buffer) for uart_rx_buffer
    uart_driver.o(.text.UART0_IRQHandler) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_tick) for uart_tick
    uart_driver.o(.ARM.exidx.text.UART0_IRQHandler) refers to uart_driver.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to uart_driver.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.DL_UART_receiveData) refers to uart_driver.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    uart_driver.o(.text.uart0_task) refers to strncmp.o(.text) for strncmp
    uart_driver.o(.text.uart0_task) refers to motor_driver.o(.text.Motor_Stop) for Motor_Stop
    uart_driver.o(.text.uart0_task) refers to memseta.o(.text) for __aeabi_memclr
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_rx_index) for uart_rx_index
    uart_driver.o(.text.uart0_task) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_tick) for uart_tick
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_rx_buffer) for uart_rx_buffer
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    uart_driver.o(.text.uart0_task) refers to user_pid.o(.bss.pid_runing) for pid_runing
    uart_driver.o(.text.uart0_task) refers to user_motor.o(.bss.left_motor) for left_motor
    uart_driver.o(.text.uart0_task) refers to user_motor.o(.bss.right_motor) for right_motor
    uart_driver.o(.ARM.exidx.text.uart0_task) refers to uart_driver.o(.text.uart0_task) for [Anonymous Symbol]
    button_driver.o(.ARM.exidx.text.key_read) refers to button_driver.o(.text.key_read) for [Anonymous Symbol]
    button_driver.o(.text.key_task) refers to button_driver.o(.text.key_read) for key_read
    button_driver.o(.text.key_task) refers to uart_driver.o(.text.my_printf) for my_printf
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_val) for key_val
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_old) for key_old
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_down) for key_down
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_up) for key_up
    button_driver.o(.text.key_task) refers to button_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    button_driver.o(.ARM.exidx.text.key_task) refers to button_driver.o(.text.key_task) for [Anonymous Symbol]
    encoder_driver.o(.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.DL_Interrupt_getPendingGroup) for DL_Interrupt_getPendingGroup
    encoder_driver.o(.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.encoder_func) for encoder_func
    encoder_driver.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.DL_Interrupt_getPendingGroup) refers to encoder_driver.o(.text.DL_Interrupt_getPendingGroup) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_func) refers to encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    encoder_driver.o(.text.encoder_func) refers to encoder_driver.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    encoder_driver.o(.text.encoder_func) refers to encoder_driver.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    encoder_driver.o(.text.encoder_func) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.ARM.exidx.text.encoder_func) refers to encoder_driver.o(.text.encoder_func) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.encoder_init) refers to encoder_driver.o(.text.encoder_init) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_update) refers to fflti.o(.text) for __aeabi_i2f
    encoder_driver.o(.text.encoder_update) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.ARM.exidx.text.encoder_update) refers to encoder_driver.o(.text.encoder_update) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.text.encoder_update) for encoder_update
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    encoder_driver.o(.ARM.exidx.text.encoder_task) refers to encoder_driver.o(.text.encoder_task) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.text.encoder_init) for encoder_init
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    encoder_driver.o(.ARM.exidx.text.encoder_config) refers to encoder_driver.o(.text.encoder_config) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.DL_GPIO_readPins) refers to encoder_driver.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to encoder_driver.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Create) refers to motor_driver.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    motor_driver.o(.text.Motor_Create) refers to motor_driver.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor_driver.o(.text.Motor_Create) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Create) refers to motor_driver.o(.text.Motor_Create) for [Anonymous Symbol]
    motor_driver.o(.ARM.exidx.text.DL_GPIO_setPins) refers to motor_driver.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    motor_driver.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to motor_driver.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(.text.Motor_SetSpeed) refers to fcmplt.o(.text) for __aeabi_fcmplt
    motor_driver.o(.text.Motor_SetSpeed) refers to fcmple.o(.text) for __aeabi_fcmple
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.Float_To_Speed1000) for Float_To_Speed1000
    motor_driver.o(.text.Motor_SetSpeed) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor_driver.o(.text.Motor_SetSpeed) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.text.Motor_SetSpeed) refers to fflti.o(.text) for __aeabi_i2f
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.Speed_To_PWM) for Speed_To_PWM
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    motor_driver.o(.ARM.exidx.text.Motor_SetSpeed) refers to motor_driver.o(.text.Motor_SetSpeed) for [Anonymous Symbol]
    motor_driver.o(.ARM.exidx.text.Motor_ValidateParams) refers to motor_driver.o(.text.Motor_ValidateParams) for [Anonymous Symbol]
    motor_driver.o(.text.Float_To_Speed1000) refers to fmul.o(.text) for __aeabi_fmul
    motor_driver.o(.text.Float_To_Speed1000) refers to roundf.o(i.roundf) for roundf
    motor_driver.o(.text.Float_To_Speed1000) refers to ffixi.o(.text) for __aeabi_f2iz
    motor_driver.o(.ARM.exidx.text.Float_To_Speed1000) refers to motor_driver.o(.text.Float_To_Speed1000) for [Anonymous Symbol]
    motor_driver.o(.text.Speed_To_PWM) refers to fcmpge.o(.text) for __aeabi_fcmpge
    motor_driver.o(.text.Speed_To_PWM) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    motor_driver.o(.text.Speed_To_PWM) refers to fmul.o(.text) for __aeabi_fmul
    motor_driver.o(.text.Speed_To_PWM) refers to fdiv.o(.text) for __aeabi_fdiv
    motor_driver.o(.text.Speed_To_PWM) refers to ffixui.o(.text) for __aeabi_f2uiz
    motor_driver.o(.ARM.exidx.text.Speed_To_PWM) refers to motor_driver.o(.text.Speed_To_PWM) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Stop) refers to motor_driver.o(.text.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(.text.Motor_Stop) refers to motor_driver.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor_driver.o(.text.Motor_Stop) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Stop) refers to motor_driver.o(.text.Motor_Stop) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_GetState) refers to motor_driver.o(.text.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(.ARM.exidx.text.Motor_GetState) refers to motor_driver.o(.text.Motor_GetState) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Enable) refers to motor_driver.o(.text.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(.text.Motor_Enable) refers to motor_driver.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor_driver.o(.text.Motor_Enable) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Enable) refers to motor_driver.o(.text.Motor_Enable) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_init) refers to pid.o(.text.pid_init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_target) refers to pid.o(.text.pid_set_target) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_params) refers to pid.o(.text.pid_set_params) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_limit) refers to pid.o(.text.pid_set_limit) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_reset) refers to pid.o(.text.pid_reset) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_positional) refers to pid.o(.text.pid_formula_positional) for pid_formula_positional
    pid.o(.text.pid_calculate_positional) refers to pid.o(.text.pid_out_limit) for pid_out_limit
    pid.o(.ARM.exidx.text.pid_calculate_positional) refers to pid.o(.text.pid_calculate_positional) for [Anonymous Symbol]
    pid.o(.text.pid_formula_positional) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.pid_formula_positional) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.ARM.exidx.text.pid_formula_positional) refers to pid.o(.text.pid_formula_positional) for [Anonymous Symbol]
    pid.o(.text.pid_out_limit) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.text.pid_out_limit) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_out_limit) refers to pid.o(.text.pid_out_limit) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_incremental) refers to pid.o(.text.pid_formula_incremental) for pid_formula_incremental
    pid.o(.text.pid_calculate_incremental) refers to pid.o(.text.pid_out_limit) for pid_out_limit
    pid.o(.ARM.exidx.text.pid_calculate_incremental) refers to pid.o(.text.pid_calculate_incremental) for [Anonymous Symbol]
    pid.o(.text.pid_formula_incremental) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.pid_formula_incremental) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.ARM.exidx.text.pid_formula_incremental) refers to pid.o(.text.pid_formula_incremental) for [Anonymous Symbol]
    pid.o(.text.pid_constrain) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.text.pid_constrain) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.ARM.exidx.text.pid_constrain) refers to pid.o(.text.pid_constrain) for [Anonymous Symbol]
    pid.o(.text.pid_app_limit_integral) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.text.pid_app_limit_integral) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_app_limit_integral) refers to pid.o(.text.pid_app_limit_integral) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_us) refers to time.o(.text.delay_us) for [Anonymous Symbol]
    time.o(.text.delay_ms) refers to time.o(.text.delay_us) for delay_us
    time.o(.ARM.exidx.text.delay_ms) refers to time.o(.text.delay_ms) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_enableConversions) for DL_ADC12_enableConversions
    no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_startConversion) for DL_ADC12_startConversion
    no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue) refers to no_mcu_ganv_grayscale_sensor.o(.text.wait_idle_with_timeout) for wait_idle_with_timeout
    no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_stopConversion) for DL_ADC12_stopConversion
    no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_disableConversions) for DL_ADC12_disableConversions
    no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_getMemResult) for DL_ADC12_getMemResult
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.adc_getValue) refers to no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_enableConversions) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_enableConversions) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_startConversion) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_startConversion) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.text.wait_idle_with_timeout) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_getStatus) for DL_ADC12_getStatus
    no_mcu_ganv_grayscale_sensor.o(.text.wait_idle_with_timeout) refers to scheduler.o(.bss.uwTick) for uwTick
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.wait_idle_with_timeout) refers to no_mcu_ganv_grayscale_sensor.o(.text.wait_idle_with_timeout) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_stopConversion) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_stopConversion) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_disableConversions) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_disableConversions) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_getMemResult) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_getMemResult) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value) refers to no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue) for adc_getValue
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Get_Analog_value) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_GPIO_setPins) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.convertAnalogToDigital) refers to no_mcu_ganv_grayscale_sensor.o(.text.convertAnalogToDigital) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.text.normalizeAnalogValues) refers to dflti.o(.text) for __aeabi_i2d
    no_mcu_ganv_grayscale_sensor.o(.text.normalizeAnalogValues) refers to dmul.o(.text) for __aeabi_dmul
    no_mcu_ganv_grayscale_sensor.o(.text.normalizeAnalogValues) refers to dfixi.o(.text) for __aeabi_d2iz
    no_mcu_ganv_grayscale_sensor.o(.text.normalizeAnalogValues) refers to dcmple.o(.text) for __aeabi_dcmple
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.normalizeAnalogValues) refers to no_mcu_ganv_grayscale_sensor.o(.text.normalizeAnalogValues) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.No_MCU_Ganv_Sensor_Init_Frist) refers to no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init_Frist) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init) refers to no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init_Frist) for No_MCU_Ganv_Sensor_Init_Frist
    no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init) refers to dfltui.o(.text) for __aeabi_ui2d
    no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init) refers to dadd.o(.text) for __aeabi_dsub
    no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init) refers to ddiv.o(.text) for __aeabi_ddiv
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.No_MCU_Ganv_Sensor_Init) refers to no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_Without_tick) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value) for Get_Analog_value
    no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_Without_tick) refers to no_mcu_ganv_grayscale_sensor.o(.text.convertAnalogToDigital) for convertAnalogToDigital
    no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_Without_tick) refers to no_mcu_ganv_grayscale_sensor.o(.text.normalizeAnalogValues) for normalizeAnalogValues
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.No_Mcu_Ganv_Sensor_Task_Without_tick) refers to no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_Without_tick) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_With_tick) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value) for Get_Analog_value
    no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_With_tick) refers to no_mcu_ganv_grayscale_sensor.o(.text.convertAnalogToDigital) for convertAnalogToDigital
    no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_With_tick) refers to no_mcu_ganv_grayscale_sensor.o(.text.normalizeAnalogValues) for normalizeAnalogValues
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.No_Mcu_Ganv_Sensor_Task_With_tick) refers to no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_With_tick) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Task_tick) refers to no_mcu_ganv_grayscale_sensor.o(.text.Task_tick) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Get_Digtal_For_User) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Digtal_For_User) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.text.Get_Normalize_For_User) refers to memcpya.o(.text) for __aeabi_memcpy
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Get_Normalize_For_User) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Normalize_For_User) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.text.Get_Anolog_Value) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value) for Get_Analog_value
    no_mcu_ganv_grayscale_sensor.o(.text.Get_Anolog_Value) refers to memcpya.o(.text) for __aeabi_memcpy
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Get_Anolog_Value) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Anolog_Value) for [Anonymous Symbol]
    no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_getStatus) refers to no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_getStatus) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_init) refers to gray_app.o(.text.user_gray_init) for user_gray_init
    scheduler.o(.text.scheduler_init) refers to user_motor.o(.text.user_motor_init) for user_motor_init
    scheduler.o(.text.scheduler_init) refers to encoder_driver.o(.text.encoder_config) for encoder_config
    scheduler.o(.text.scheduler_init) refers to user_pid.o(.text.PID_Init) for PID_Init
    scheduler.o(.text.scheduler_init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.scheduler_init) refers to scheduler.o(.text.scheduler_init) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.data.scheduler_task) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.scheduler_run) refers to scheduler.o(.text.scheduler_run) for [Anonymous Symbol]
    scheduler.o(.text.SysTick_Handler) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.ARM.exidx.text.SysTick_Handler) refers to scheduler.o(.text.SysTick_Handler) for [Anonymous Symbol]
    scheduler.o(.text.DL_Delay) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.ARM.exidx.text.DL_Delay) refers to scheduler.o(.text.DL_Delay) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to uart_driver.o(.text.uart0_task) for uart0_task
    scheduler.o(.data.scheduler_task) refers to gray_app.o(.text.user_gray_task) for user_gray_task
    gray_app.o(.text.user_gray_init) refers to no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init) for No_MCU_Ganv_Sensor_Init
    gray_app.o(.text.user_gray_init) refers to time.o(.text.delay_ms) for delay_ms
    gray_app.o(.text.user_gray_init) refers to gray_app.o(.bss.sensor) for sensor
    gray_app.o(.text.user_gray_init) refers to gray_app.o(.data.white) for white
    gray_app.o(.text.user_gray_init) refers to gray_app.o(.data.black) for black
    gray_app.o(.ARM.exidx.text.user_gray_init) refers to gray_app.o(.text.user_gray_init) for [Anonymous Symbol]
    gray_app.o(.ARM.exidx.text.Get_Digtal_wei) refers to gray_app.o(.text.Get_Digtal_wei) for [Anonymous Symbol]
    gray_app.o(.text.user_gray_task) refers to no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_Without_tick) for No_Mcu_Ganv_Sensor_Task_Without_tick
    gray_app.o(.text.user_gray_task) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Anolog_Value) for Get_Anolog_Value
    gray_app.o(.text.user_gray_task) refers to uart_driver.o(.text.my_printf) for my_printf
    gray_app.o(.text.user_gray_task) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Digtal_For_User) for Get_Digtal_For_User
    gray_app.o(.text.user_gray_task) refers to no_mcu_ganv_grayscale_sensor.o(.text.Get_Normalize_For_User) for Get_Normalize_For_User
    gray_app.o(.text.user_gray_task) refers to time.o(.text.delay_ms) for delay_ms
    gray_app.o(.text.user_gray_task) refers to gray_app.o(.bss.sensor) for sensor
    gray_app.o(.text.user_gray_task) refers to gray_app.o(.bss.Anolog) for Anolog
    gray_app.o(.text.user_gray_task) refers to gray_app.o(.rodata.str1.1) for [Anonymous Symbol]
    gray_app.o(.text.user_gray_task) refers to gray_app.o(.bss.Digtal) for Digtal
    gray_app.o(.text.user_gray_task) refers to gray_app.o(.bss.Normal) for Normal
    gray_app.o(.ARM.exidx.text.user_gray_task) refers to gray_app.o(.text.user_gray_task) for [Anonymous Symbol]
    user_motor.o(.text.user_motor_init) refers to motor_driver.o(.text.Motor_Create) for Motor_Create
    user_motor.o(.text.user_motor_init) refers to user_motor.o(.bss.left_motor) for left_motor
    user_motor.o(.text.user_motor_init) refers to user_motor.o(.bss.right_motor) for right_motor
    user_motor.o(.ARM.exidx.text.user_motor_init) refers to user_motor.o(.text.user_motor_init) for [Anonymous Symbol]
    user_motor.o(.text.motor_set_l) refers to motor_driver.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    user_motor.o(.text.motor_set_l) refers to user_motor.o(.bss.left_motor) for left_motor
    user_motor.o(.ARM.exidx.text.motor_set_l) refers to user_motor.o(.text.motor_set_l) for [Anonymous Symbol]
    user_motor.o(.text.motor_set_r) refers to motor_driver.o(.text.Motor_SetSpeed) for Motor_SetSpeed
    user_motor.o(.text.motor_set_r) refers to user_motor.o(.bss.right_motor) for right_motor
    user_motor.o(.ARM.exidx.text.motor_set_r) refers to user_motor.o(.text.motor_set_r) for [Anonymous Symbol]
    user_pid.o(.text.PID_Init) refers to pid.o(.text.pid_init) for pid_init
    user_pid.o(.text.PID_Init) refers to pid.o(.text.pid_set_target) for pid_set_target
    user_pid.o(.text.PID_Init) refers to user_pid.o(.data.pid_params_left) for pid_params_left
    user_pid.o(.text.PID_Init) refers to user_pid.o(.bss.pid_speed_left) for pid_speed_left
    user_pid.o(.text.PID_Init) refers to user_pid.o(.data.pid_params_right) for pid_params_right
    user_pid.o(.text.PID_Init) refers to user_pid.o(.bss.pid_speed_right) for pid_speed_right
    user_pid.o(.text.PID_Init) refers to user_pid.o(.data.pid_params_line) for pid_params_line
    user_pid.o(.text.PID_Init) refers to user_pid.o(.bss.pid_line) for pid_line
    user_pid.o(.text.PID_Init) refers to user_pid.o(.data.basic_speed) for basic_speed
    user_pid.o(.ARM.exidx.text.PID_Init) refers to user_pid.o(.text.PID_Init) for [Anonymous Symbol]
    user_pid.o(.text.Line_PID_control) refers to pid.o(.text.pid_calculate_positional) for pid_calculate_positional
    user_pid.o(.text.Line_PID_control) refers to ffixi.o(.text) for __aeabi_f2iz
    user_pid.o(.text.Line_PID_control) refers to fflti.o(.text) for __aeabi_i2f
    user_pid.o(.text.Line_PID_control) refers to pid.o(.text.pid_constrain) for pid_constrain
    user_pid.o(.text.Line_PID_control) refers to fadd.o(.text) for __aeabi_fsub
    user_pid.o(.text.Line_PID_control) refers to pid.o(.text.pid_set_target) for pid_set_target
    user_pid.o(.text.Line_PID_control) refers to gray_app.o(.bss.g_line_position_error) for g_line_position_error
    user_pid.o(.text.Line_PID_control) refers to user_pid.o(.bss.pid_line) for pid_line
    user_pid.o(.text.Line_PID_control) refers to user_pid.o(.data.pid_params_line) for pid_params_line
    user_pid.o(.text.Line_PID_control) refers to user_pid.o(.data.basic_speed) for basic_speed
    user_pid.o(.text.Line_PID_control) refers to user_pid.o(.bss.pid_speed_left) for pid_speed_left
    user_pid.o(.text.Line_PID_control) refers to user_pid.o(.bss.pid_speed_right) for pid_speed_right
    user_pid.o(.ARM.exidx.text.Line_PID_control) refers to user_pid.o(.text.Line_PID_control) for [Anonymous Symbol]
    user_pid.o(.text.PID_Task) refers to pid.o(.text.pid_calculate_positional) for pid_calculate_positional
    user_pid.o(.text.PID_Task) refers to pid.o(.text.pid_constrain) for pid_constrain
    user_pid.o(.text.PID_Task) refers to fdiv.o(.text) for __aeabi_fdiv
    user_pid.o(.text.PID_Task) refers to fmul.o(.text) for __aeabi_fmul
    user_pid.o(.text.PID_Task) refers to user_motor.o(.text.motor_set_r) for motor_set_r
    user_pid.o(.text.PID_Task) refers to f2d.o(.text) for __aeabi_f2d
    user_pid.o(.text.PID_Task) refers to uart_driver.o(.text.my_printf) for my_printf
    user_pid.o(.text.PID_Task) refers to user_pid.o(.bss.pid_runing) for pid_runing
    user_pid.o(.text.PID_Task) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    user_pid.o(.text.PID_Task) refers to user_pid.o(.bss.pid_speed_left) for pid_speed_left
    user_pid.o(.text.PID_Task) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    user_pid.o(.text.PID_Task) refers to user_pid.o(.bss.pid_speed_right) for pid_speed_right
    user_pid.o(.text.PID_Task) refers to user_pid.o(.data.pid_params_left) for pid_params_left
    user_pid.o(.text.PID_Task) refers to user_pid.o(.data.pid_params_right) for pid_params_right
    user_pid.o(.text.PID_Task) refers to user_pid.o(.rodata.str1.1) for [Anonymous Symbol]
    user_pid.o(.ARM.exidx.text.PID_Task) refers to user_pid.o(.text.PID_Task) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers to fadd.o(.text) for __aeabi_fsub
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpeq.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frnd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_mspm0g350x_uvision.o(HEAP), (2048 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_LEFT_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC_VOLTAGE_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (52 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (52 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFPCLK), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setMFPCLKSource), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_getClockStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_initSingleSample), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_configConversionMem), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setPowerDownMode), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_setSampleTime0), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_ADC12_enableConversions), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SysTick_Config), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.user_config), (8 bytes).
    Removing main.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing main.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing uart_driver.o(.text), (0 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart_send_char), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart_send_string), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart0_task), (8 bytes).
    Removing button_driver.o(.text), (0 bytes).
    Removing button_driver.o(.text.key_read), (18 bytes).
    Removing button_driver.o(.ARM.exidx.text.key_read), (8 bytes).
    Removing button_driver.o(.text.key_task), (84 bytes).
    Removing button_driver.o(.ARM.exidx.text.key_task), (8 bytes).
    Removing button_driver.o(.bss.key_val), (1 bytes).
    Removing button_driver.o(.bss.key_old), (1 bytes).
    Removing button_driver.o(.bss.key_down), (1 bytes).
    Removing button_driver.o(.bss.key_up), (1 bytes).
    Removing button_driver.o(.rodata.str1.1), (15 bytes).
    Removing encoder_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.DL_Interrupt_getPendingGroup), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_func), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing encoder_driver.o(.text.encoder_update), (104 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_update), (8 bytes).
    Removing encoder_driver.o(.text.encoder_task), (24 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_task), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_config), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing motor_driver.o(.text), (0 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Create), (8 bytes).
    Removing motor_driver.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing motor_driver.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing motor_driver.o(.text.Motor_SetSpeed), (360 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_SetSpeed), (8 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_ValidateParams), (8 bytes).
    Removing motor_driver.o(.text.Float_To_Speed1000), (32 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Float_To_Speed1000), (8 bytes).
    Removing motor_driver.o(.text.Speed_To_PWM), (140 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Speed_To_PWM), (8 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Stop), (8 bytes).
    Removing motor_driver.o(.text.Motor_GetState), (44 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_GetState), (8 bytes).
    Removing motor_driver.o(.text.Motor_Enable), (110 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Enable), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.ARM.exidx.text.pid_init), (8 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_target), (8 bytes).
    Removing pid.o(.text.pid_set_params), (34 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_params), (8 bytes).
    Removing pid.o(.text.pid_set_limit), (16 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_limit), (8 bytes).
    Removing pid.o(.text.pid_reset), (38 bytes).
    Removing pid.o(.ARM.exidx.text.pid_reset), (8 bytes).
    Removing pid.o(.text.pid_calculate_positional), (34 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_positional), (8 bytes).
    Removing pid.o(.text.pid_formula_positional), (132 bytes).
    Removing pid.o(.ARM.exidx.text.pid_formula_positional), (8 bytes).
    Removing pid.o(.text.pid_out_limit), (72 bytes).
    Removing pid.o(.ARM.exidx.text.pid_out_limit), (8 bytes).
    Removing pid.o(.text.pid_calculate_incremental), (34 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_incremental), (8 bytes).
    Removing pid.o(.text.pid_formula_incremental), (164 bytes).
    Removing pid.o(.ARM.exidx.text.pid_formula_incremental), (8 bytes).
    Removing pid.o(.text.pid_constrain), (62 bytes).
    Removing pid.o(.ARM.exidx.text.pid_constrain), (8 bytes).
    Removing pid.o(.text.pid_app_limit_integral), (64 bytes).
    Removing pid.o(.ARM.exidx.text.pid_app_limit_integral), (8 bytes).
    Removing time.o(.text), (0 bytes).
    Removing time.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing time.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.text), (0 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.adc_getValue), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_enableConversions), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_startConversion), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.wait_idle_with_timeout), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_stopConversion), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_disableConversions), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_getMemResult), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Get_Analog_value), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.convertAnalogToDigital), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.normalizeAnalogValues), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.No_MCU_Ganv_Sensor_Init_Frist), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.No_MCU_Ganv_Sensor_Init), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.No_Mcu_Ganv_Sensor_Task_Without_tick), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_With_tick), (92 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.No_Mcu_Ganv_Sensor_Task_With_tick), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.text.Task_tick), (18 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Task_tick), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Get_Digtal_For_User), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Get_Normalize_For_User), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.Get_Anolog_Value), (8 bytes).
    Removing no_mcu_ganv_grayscale_sensor.o(.ARM.exidx.text.DL_ADC12_getStatus), (8 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_run), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing scheduler.o(.text.DL_Delay), (40 bytes).
    Removing scheduler.o(.ARM.exidx.text.DL_Delay), (8 bytes).
    Removing gray_app.o(.text), (0 bytes).
    Removing gray_app.o(.ARM.exidx.text.user_gray_init), (8 bytes).
    Removing gray_app.o(.text.Get_Digtal_wei), (60 bytes).
    Removing gray_app.o(.ARM.exidx.text.Get_Digtal_wei), (8 bytes).
    Removing gray_app.o(.ARM.exidx.text.user_gray_task), (8 bytes).
    Removing gray_app.o(.bss.g_line_position_error), (4 bytes).
    Removing user_motor.o(.text), (0 bytes).
    Removing user_motor.o(.ARM.exidx.text.user_motor_init), (8 bytes).
    Removing user_motor.o(.text.motor_set_l), (24 bytes).
    Removing user_motor.o(.ARM.exidx.text.motor_set_l), (8 bytes).
    Removing user_motor.o(.text.motor_set_r), (24 bytes).
    Removing user_motor.o(.ARM.exidx.text.motor_set_r), (8 bytes).
    Removing user_pid.o(.text), (0 bytes).
    Removing user_pid.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing user_pid.o(.text.Line_PID_control), (132 bytes).
    Removing user_pid.o(.ARM.exidx.text.Line_PID_control), (8 bytes).
    Removing user_pid.o(.text.PID_Task), (208 bytes).
    Removing user_pid.o(.ARM.exidx.text.PID_Task), (8 bytes).
    Removing user_pid.o(.rodata.str1.1), (12 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initTimerMode), (240 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).
    Removing fadd.o(.text), (178 bytes).
    Removing fmul.o(.text), (122 bytes).
    Removing fdiv.o(.text), (124 bytes).
    Removing fcmple.o(.text), (28 bytes).
    Removing fcmplt.o(.text), (28 bytes).
    Removing fcmpge.o(.text), (28 bytes).
    Removing fcmpeq.o(.text), (28 bytes).
    Removing fflti.o(.text), (22 bytes).
    Removing ffixi.o(.text), (50 bytes).
    Removing ffixui.o(.text), (40 bytes).
    Removing f2d.o(.text), (40 bytes).
    Removing fepilogue.o(.text), (130 bytes).
    Removing frnd.o(.text), (62 bytes).

350 unused section(s) (total 11317 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmplt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpge.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpeq.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmple.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    No_Mcu_Ganv_Grayscale_Sensor.c           0x00000000   Number         0  no_mcu_ganv_grayscale_sensor.o ABSOLUTE
    Time.c                                   0x00000000   Number         0  time.o ABSOLUTE
    button_driver.c                          0x00000000   Number         0  button_driver.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    encoder_driver.c                         0x00000000   Number         0  encoder_driver.o ABSOLUTE
    gray_app.c                               0x00000000   Number         0  gray_app.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    motor_driver.c                           0x00000000   Number         0  motor_driver.o ABSOLUTE
    pid.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    uart_driver.c                            0x00000000   Number         0  uart_driver.o ABSOLUTE
    user_motor.c                             0x00000000   Number         0  user_motor.o ABSOLUTE
    user_pid.c                               0x00000000   Number         0  user_pid.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  memcpya.o(.text)
    .text                                    0x0000010c   Section        0  memseta.o(.text)
    .text                                    0x00000130   Section        0  strncmp.o(.text)
    .text                                    0x00000150   Section        0  dadd.o(.text)
    .text                                    0x000002b4   Section        0  dmul.o(.text)
    .text                                    0x00000384   Section        0  ddiv.o(.text)
    .text                                    0x00000474   Section        0  dcmple.o(.text)
    .text                                    0x000004a0   Section        0  dflti.o(.text)
    .text                                    0x000004c8   Section        0  dfltui.o(.text)
    .text                                    0x000004e4   Section        0  dfixi.o(.text)
    .text                                    0x0000052c   Section        0  uidiv_div0.o(.text)
    .text                                    0x0000056a   Section        0  uldiv.o(.text)
    .text                                    0x000005ca   Section        0  llshl.o(.text)
    .text                                    0x000005ea   Section        0  llushr.o(.text)
    .text                                    0x0000060c   Section        0  llsshr.o(.text)
    .text                                    0x00000632   Section        0  iusefp.o(.text)
    .text                                    0x00000632   Section        0  depilogue.o(.text)
    .text                                    0x000006f0   Section        0  dfixul.o(.text)
    .text                                    0x00000730   Section       40  cdrcmple.o(.text)
    .text                                    0x00000758   Section       48  init.o(.text)
    DL_ADC12_configConversionMem             0x00000789   Thumb Code    74  ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem)
    [Anonymous Symbol]                       0x00000788   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem)
    DL_ADC12_disableConversions              0x000007d3   Thumb Code    22  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_disableConversions)
    [Anonymous Symbol]                       0x000007d2   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_disableConversions)
    DL_ADC12_enableConversions               0x000007e9   Thumb Code    22  ti_msp_dl_config.o(.text.DL_ADC12_enableConversions)
    [Anonymous Symbol]                       0x000007e8   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enableConversions)
    DL_ADC12_enableConversions               0x000007ff   Thumb Code    22  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_enableConversions)
    [Anonymous Symbol]                       0x000007fe   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_enableConversions)
    DL_ADC12_enablePower                     0x00000815   Thumb Code    20  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    [Anonymous Symbol]                       0x00000814   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_enablePower)
    DL_ADC12_getMemResult                    0x00000829   Thumb Code    44  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_getMemResult)
    [Anonymous Symbol]                       0x00000828   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_getMemResult)
    __arm_cp.6_0                             0x00000854   Number         4  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_getMemResult)
    DL_ADC12_getStatus                       0x00000859   Thumb Code    16  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_getStatus)
    [Anonymous Symbol]                       0x00000858   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_getStatus)
    DL_ADC12_initSingleSample                0x00000869   Thumb Code    60  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    [Anonymous Symbol]                       0x00000868   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.47_1                            0x000008a4   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.47_2                            0x000008a8   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    __arm_cp.47_3                            0x000008ac   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_initSingleSample)
    DL_ADC12_reset                           0x000008b1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    [Anonymous Symbol]                       0x000008b0   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_reset)
    [Anonymous Symbol]                       0x000008c0   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x000008f8   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x000008fc   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_ADC12_setPowerDownMode                0x00000901   Thumb Code    30  ti_msp_dl_config.o(.text.DL_ADC12_setPowerDownMode)
    [Anonymous Symbol]                       0x00000900   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_setPowerDownMode)
    DL_ADC12_setSampleTime0                  0x00000921   Thumb Code    20  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    [Anonymous Symbol]                       0x00000920   Section        0  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    __arm_cp.50_0                            0x00000934   Number         4  ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0)
    DL_ADC12_startConversion                 0x00000939   Thumb Code    24  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_startConversion)
    [Anonymous Symbol]                       0x00000938   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_startConversion)
    DL_ADC12_stopConversion                  0x00000951   Thumb Code    24  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_stopConversion)
    [Anonymous Symbol]                       0x00000950   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_stopConversion)
    __arm_cp.4_0                             0x00000968   Number         4  no_mcu_ganv_grayscale_sensor.o(.text.DL_ADC12_stopConversion)
    [Anonymous Symbol]                       0x0000096c   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    DL_Common_updateReg                      0x00000977   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x00000976   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_GPIO_clearInterruptStatus             0x000009a1   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x000009a0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearInterruptStatus             0x000009b9   Thumb Code    24  encoder_driver.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x000009b8   Section        0  encoder_driver.o(.text.DL_GPIO_clearInterruptStatus)
    __arm_cp.9_0                             0x000009d0   Number         4  encoder_driver.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearPins                        0x000009d5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000009d4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x000009e9   Thumb Code    20  motor_driver.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000009e8   Section        0  motor_driver.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x000009fd   Thumb Code    20  no_mcu_ganv_grayscale_sensor.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000009fc   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableInterrupt                  0x00000a11   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    [Anonymous Symbol]                       0x00000a10   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    __arm_cp.29_0                            0x00000a28   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    DL_GPIO_enableOutput                     0x00000a2d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00000a2c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    __arm_cp.22_0                            0x00000a40   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x00000a45   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x00000a44   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    __arm_cp.16_0                            0x00000a58   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_getEnabledInterruptStatus        0x00000a5d   Thumb Code    20  encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus)
    [Anonymous Symbol]                       0x00000a5c   Section        0  encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus)
    __arm_cp.7_0                             0x00000a70   Number         4  encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus)
    DL_GPIO_initDigitalInputFeatures         0x00000a75   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    [Anonymous Symbol]                       0x00000a74   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    __arm_cp.25_0                            0x00000aa0   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    DL_GPIO_initDigitalOutput                0x00000aa5   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x00000aa4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initPeripheralAnalogFunction     0x00000ab9   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    [Anonymous Symbol]                       0x00000ab8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    DL_GPIO_initPeripheralInputFunction      0x00000acd   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x00000acc   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    __arm_cp.23_0                            0x00000ae4   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralOutputFunction     0x00000ae9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x00000ae8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.21_0                            0x00000b00   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x00000b05   Thumb Code    22  encoder_driver.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000b04   Section        0  encoder_driver.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00000b1d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00000b1c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.12_0                            0x00000b2c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    __arm_cp.12_1                            0x00000b30   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setLowerPinsPolarity             0x00000b35   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    [Anonymous Symbol]                       0x00000b34   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    __arm_cp.27_0                            0x00000b4c   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    DL_GPIO_setPins                          0x00000b51   Thumb Code    20  motor_driver.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000b50   Section        0  motor_driver.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x00000b65   Thumb Code    20  no_mcu_ganv_grayscale_sensor.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x00000b64   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.DL_GPIO_setPins)
    __arm_cp.8_0                             0x00000b78   Number         4  no_mcu_ganv_grayscale_sensor.o(.text.DL_GPIO_setPins)
    DL_Interrupt_getPendingGroup             0x00000b7d   Thumb Code    24  encoder_driver.o(.text.DL_Interrupt_getPendingGroup)
    [Anonymous Symbol]                       0x00000b7c   Section        0  encoder_driver.o(.text.DL_Interrupt_getPendingGroup)
    __arm_cp.1_0                             0x00000b94   Number         4  encoder_driver.o(.text.DL_Interrupt_getPendingGroup)
    [Anonymous Symbol]                       0x00000b98   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00000c4c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00000c50   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00000c54   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_disableHFXT                    0x00000c59   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x00000c58   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00000c65   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00000c64   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.34_0                            0x00000c74   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_enableMFCLK                    0x00000c79   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    [Anonymous Symbol]                       0x00000c78   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    DL_SYSCTL_enableMFPCLK                   0x00000c89   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK)
    [Anonymous Symbol]                       0x00000c88   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK)
    __arm_cp.37_0                            0x00000c94   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK)
    DL_SYSCTL_getClockStatus                 0x00000c99   Thumb Code     8  ti_msp_dl_config.o(.text.DL_SYSCTL_getClockStatus)
    [Anonymous Symbol]                       0x00000c98   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_getClockStatus)
    DL_SYSCTL_setBORThreshold                0x00000ca1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x00000ca0   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.30_0                            0x00000cb4   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    DL_SYSCTL_setFlashWaitState              0x00000cb9   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00000cb8   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00000cd4   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x00000d20   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_setMFPCLKSource                0x00000d25   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource)
    [Anonymous Symbol]                       0x00000d24   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource)
    __arm_cp.38_0                            0x00000d40   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource)
    DL_SYSCTL_setSYSOSCFreq                  0x00000d45   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x00000d44   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.32_0                            0x00000d5c   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_SYSCTL_setULPCLKDivider               0x00000d61   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00000d60   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00000d78   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00000d98   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x00000d9c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_Timer_enableClock                     0x00000da1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x00000da0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enablePower                     0x00000db1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00000db0   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    __arm_cp.17_0                            0x00000dc4   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00000dc8   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x00000ebc   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x00000ec0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_3                            0x00000ec4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x00000ec8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x00000ecc   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x00000ed0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_reset                           0x00000ed5   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x00000ed4   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    __arm_cp.13_0                            0x00000ee4   Number         4  ti_msp_dl_config.o(.text.DL_Timer_reset)
    __arm_cp.13_1                            0x00000ee8   Number         4  ti_msp_dl_config.o(.text.DL_Timer_reset)
    DL_Timer_setCCPDirection                 0x00000eed   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00000eec   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00000f00   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x00000f18   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00000f1c   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00000f30   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00000f34   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00000f40   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00000f44   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00000f5c   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_Timer_setCounterControl               0x00000f61   Thumb Code    52  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    [Anonymous Symbol]                       0x00000f60   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.40_0                            0x00000f94   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.40_1                            0x00000f98   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    DL_UART_enable                           0x00000f9d   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x00000f9c   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableInterrupt                  0x00000fb5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x00000fb4   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    __arm_cp.45_0                            0x00000fcc   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enablePower                      0x00000fd1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x00000fd0   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.18_0                            0x00000fe4   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x00000fe9   Thumb Code    18  uart_driver.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00000fe8   Section        0  uart_driver.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00000ffc   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x0000103c   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00001040   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_isBusy                           0x00001045   Thumb Code    20  uart_driver.o(.text.DL_UART_isBusy)
    [Anonymous Symbol]                       0x00001044   Section        0  uart_driver.o(.text.DL_UART_isBusy)
    __arm_cp.1_0                             0x00001058   Number         4  uart_driver.o(.text.DL_UART_isBusy)
    DL_UART_receiveData                      0x0000105d   Thumb Code    16  uart_driver.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x0000105c   Section        0  uart_driver.o(.text.DL_UART_receiveData)
    __arm_cp.7_0                             0x0000106c   Number         4  uart_driver.o(.text.DL_UART_receiveData)
    DL_UART_reset                            0x00001071   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x00001070   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.14_0                            0x00001080   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.14_1                            0x00001084   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x00001089   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00001088   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.44_0                            0x000010c4   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.44_1                            0x000010c8   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.44_2                            0x000010cc   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.44_3                            0x000010d0   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x000010d4   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x000010e7   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x000010e6   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    DL_UART_transmitData                     0x00001105   Thumb Code    22  uart_driver.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x00001104   Section        0  uart_driver.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x0000111a   Section        0  encoder_driver.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x00001130   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value)
    __arm_cp.7_0                             0x00001200   Number         4  no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value)
    [Anonymous Symbol]                       0x00001204   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.Get_Anolog_Value)
    [Anonymous Symbol]                       0x00001240   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.Get_Digtal_For_User)
    [Anonymous Symbol]                       0x0000124e   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.Get_Normalize_For_User)
    [Anonymous Symbol]                       0x00001286   Section        0  motor_driver.o(.text.Motor_Create)
    [Anonymous Symbol]                       0x00001304   Section        0  motor_driver.o(.text.Motor_Stop)
    Motor_ValidateParams                     0x0000135b   Thumb Code    58  motor_driver.o(.text.Motor_ValidateParams)
    [Anonymous Symbol]                       0x0000135a   Section        0  motor_driver.o(.text.Motor_ValidateParams)
    [Anonymous Symbol]                       0x00001394   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init)
    __arm_cp.13_0                            0x00001518   Number         4  no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init)
    [Anonymous Symbol]                       0x0000151c   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init_Frist)
    [Anonymous Symbol]                       0x0000158e   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
    [Anonymous Symbol]                       0x000015d0   Section        0  user_pid.o(.text.PID_Init)
    __arm_cp.0_0                             0x00001640   Number         4  user_pid.o(.text.PID_Init)
    __arm_cp.0_1                             0x00001644   Number         4  user_pid.o(.text.PID_Init)
    __arm_cp.0_2                             0x00001648   Number         4  user_pid.o(.text.PID_Init)
    __arm_cp.0_3                             0x0000164c   Number         4  user_pid.o(.text.PID_Init)
    __arm_cp.0_4                             0x00001650   Number         4  user_pid.o(.text.PID_Init)
    __arm_cp.0_5                             0x00001654   Number         4  user_pid.o(.text.PID_Init)
    __arm_cp.0_6                             0x00001658   Number         4  user_pid.o(.text.PID_Init)
    [Anonymous Symbol]                       0x0000165c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    __arm_cp.7_0                             0x000016b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    [Anonymous Symbol]                       0x000016b8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x000017bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x000017c0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init)
    __arm_cp.4_0                             0x00001820   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init)
    __arm_cp.4_2                             0x00001824   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init)
    [Anonymous Symbol]                       0x00001828   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init)
    __arm_cp.5_0                             0x00001888   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init)
    __arm_cp.5_2                             0x0000188c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init)
    [Anonymous Symbol]                       0x00001890   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    __arm_cp.9_0                             0x000018a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    [Anonymous Symbol]                       0x000018ac   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00001900   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00001904   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.8_0                             0x00001910   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001914   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.6_0                             0x00001950   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.6_2                             0x00001954   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00001958   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00001988   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x0000198c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x000019f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x000019f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x000019f8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x000019fc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Config                           0x00001a01   Thumb Code    68  ti_msp_dl_config.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x00001a00   Section        0  ti_msp_dl_config.o(.text.SysTick_Config)
    __arm_cp.52_2                            0x00001a44   Number         4  ti_msp_dl_config.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x00001a48   Section        0  scheduler.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x00001a54   Section        0  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.5_0                             0x00001a94   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.5_1                             0x00001a98   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.5_2                             0x00001a9c   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.5_3                             0x00001aa0   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.5_4                             0x00001aa4   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __NVIC_ClearPendingIRQ                   0x00001aa9   Thumb Code    40  main.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00001aa8   Section        0  main.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.2_0                             0x00001ad0   Number         4  main.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_EnableIRQ                         0x00001ad5   Thumb Code    40  main.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x00001ad4   Section        0  main.o(.text.__NVIC_EnableIRQ)
    __arm_cp.3_0                             0x00001afc   Number         4  main.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x00001b01   Thumb Code   124  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00001b00   Section        0  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.54_0                            0x00001b7c   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.54_1                            0x00001b80   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00001b84   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue)
    [Anonymous Symbol]                       0x00001bd4   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.convertAnalogToDigital)
    [Anonymous Symbol]                       0x00001c40   Section        0  time.o(.text.delay_ms)
    [Anonymous Symbol]                       0x00001c58   Section        0  time.o(.text.delay_us)
    __arm_cp.0_0                             0x00001cc4   Number         4  time.o(.text.delay_us)
    __arm_cp.0_1                             0x00001cc8   Number         4  time.o(.text.delay_us)
    [Anonymous Symbol]                       0x00001ccc   Section        0  encoder_driver.o(.text.encoder_config)
    __arm_cp.6_0                             0x00001ce4   Number         4  encoder_driver.o(.text.encoder_config)
    __arm_cp.6_1                             0x00001ce8   Number         4  encoder_driver.o(.text.encoder_config)
    encoder_func                             0x00001ced   Thumb Code   248  encoder_driver.o(.text.encoder_func)
    [Anonymous Symbol]                       0x00001cec   Section        0  encoder_driver.o(.text.encoder_func)
    __arm_cp.2_0                             0x00001de4   Number         4  encoder_driver.o(.text.encoder_func)
    __arm_cp.2_1                             0x00001de8   Number         4  encoder_driver.o(.text.encoder_func)
    [Anonymous Symbol]                       0x00001dec   Section        0  encoder_driver.o(.text.encoder_init)
    [Anonymous Symbol]                       0x00001e16   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x00001e32   Section        0  uart_driver.o(.text.my_printf)
    [Anonymous Symbol]                       0x00001e6c   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.normalizeAnalogValues)
    [Anonymous Symbol]                       0x00001f16   Section        0  pid.o(.text.pid_init)
    [Anonymous Symbol]                       0x00001f66   Section        0  pid.o(.text.pid_set_target)
    [Anonymous Symbol]                       0x00001f78   Section        0  scheduler.o(.text.scheduler_init)
    [Anonymous Symbol]                       0x00001f94   Section        0  scheduler.o(.text.scheduler_run)
    __arm_cp.1_0                             0x00001ffc   Number         4  scheduler.o(.text.scheduler_run)
    __arm_cp.1_2                             0x00002000   Number         4  scheduler.o(.text.scheduler_run)
    [Anonymous Symbol]                       0x00002004   Section        0  uart_driver.o(.text.uart0_task)
    __arm_cp.8_0                             0x00002074   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.8_2                             0x00002078   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.8_3                             0x0000207c   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.8_4                             0x00002080   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.8_5                             0x00002084   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.8_6                             0x00002088   Number         4  uart_driver.o(.text.uart0_task)
    [Anonymous Symbol]                       0x0000208c   Section        0  uart_driver.o(.text.uart_send_char)
    [Anonymous Symbol]                       0x000020b4   Section        0  uart_driver.o(.text.uart_send_string)
    [Anonymous Symbol]                       0x000020e0   Section        0  main.o(.text.user_config)
    [Anonymous Symbol]                       0x000020fc   Section        0  gray_app.o(.text.user_gray_init)
    __arm_cp.0_1                             0x00002110   Number         4  gray_app.o(.text.user_gray_init)
    __arm_cp.0_2                             0x00002114   Number         4  gray_app.o(.text.user_gray_init)
    [Anonymous Symbol]                       0x00002118   Section        0  gray_app.o(.text.user_gray_task)
    __arm_cp.2_0                             0x000021dc   Number         4  gray_app.o(.text.user_gray_task)
    __arm_cp.2_1                             0x000021e0   Number         4  gray_app.o(.text.user_gray_task)
    __arm_cp.2_2                             0x000021e4   Number         4  gray_app.o(.text.user_gray_task)
    __arm_cp.2_3                             0x000021e8   Number         4  gray_app.o(.text.user_gray_task)
    __arm_cp.2_4                             0x000021ec   Number         4  gray_app.o(.text.user_gray_task)
    __arm_cp.2_5                             0x000021f0   Number         4  gray_app.o(.text.user_gray_task)
    __arm_cp.2_6                             0x000021f4   Number         4  gray_app.o(.text.user_gray_task)
    __arm_cp.2_7                             0x000021f8   Number         4  gray_app.o(.text.user_gray_task)
    [Anonymous Symbol]                       0x000021fc   Section        0  user_motor.o(.text.user_motor_init)
    __arm_cp.0_0                             0x00002244   Number         4  user_motor.o(.text.user_motor_init)
    __arm_cp.0_1                             0x00002248   Number         4  user_motor.o(.text.user_motor_init)
    __arm_cp.0_2                             0x0000224c   Number         4  user_motor.o(.text.user_motor_init)
    __arm_cp.0_3                             0x00002250   Number         4  user_motor.o(.text.user_motor_init)
    __arm_cp.0_4                             0x00002254   Number         4  user_motor.o(.text.user_motor_init)
    __arm_cp.0_5                             0x00002258   Number         4  user_motor.o(.text.user_motor_init)
    wait_idle_with_timeout                   0x0000225d   Thumb Code    72  no_mcu_ganv_grayscale_sensor.o(.text.wait_idle_with_timeout)
    [Anonymous Symbol]                       0x0000225c   Section        0  no_mcu_ganv_grayscale_sensor.o(.text.wait_idle_with_timeout)
    __arm_cp.3_0                             0x000022a4   Number         4  no_mcu_ganv_grayscale_sensor.o(.text.wait_idle_with_timeout)
    i.__0vsnprintf                           0x000022a8   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_clz                              0x000022d8   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x00002308   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00002318   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00002320   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x00002331   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x00002330   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x000024a5   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x000024a4   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x00002b91   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x00002b90   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x00002bb1   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x00002bb0   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x00002bdd   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x00002bdc   Section        0  printfa.o(i._snputc)
    gADC_VOLTAGEClockConfig                  0x00002bf4   Data           8  ti_msp_dl_config.o(.rodata.gADC_VOLTAGEClockConfig)
    [Anonymous Symbol]                       0x00002bf4   Section        0  ti_msp_dl_config.o(.rodata.gADC_VOLTAGEClockConfig)
    gMOTOR_PWM_LEFTClockConfig               0x00002bfc   Data           3  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTClockConfig)
    [Anonymous Symbol]                       0x00002bfc   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTClockConfig)
    gMOTOR_PWM_LEFTConfig                    0x00002c00   Data           8  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTConfig)
    [Anonymous Symbol]                       0x00002c00   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTConfig)
    gMOTOR_PWM_RIGHTClockConfig              0x00002c08   Data           3  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTClockConfig)
    [Anonymous Symbol]                       0x00002c08   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTClockConfig)
    gMOTOR_PWM_RIGHTConfig                   0x00002c0c   Data           8  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTConfig)
    [Anonymous Symbol]                       0x00002c0c   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTConfig)
    gSYSPLLConfig                            0x00002c14   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x00002c14   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gUART_0ClockConfig                       0x00002c3c   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00002c3c   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x00002c3e   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00002c3e   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00002c48   Section        0  uart_driver.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x00002c53   Section        0  gray_app.o(.rodata.str1.1)
    scheduler_task                           0x20200050   Data          24  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x20200050   Section        0  scheduler.o(.data.scheduler_task)
    STACK                                    0x20200390   Section    16384  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000000dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000000df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_memcpy                           0x000000e9   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x000000e9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x000000e9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0000010d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0000010d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0000010d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0000011b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0000011b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0000011b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0000011f   Thumb Code    18  memseta.o(.text)
    strncmp                                  0x00000131   Thumb Code    30  strncmp.o(.text)
    __aeabi_dadd                             0x00000151   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x00000299   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x000002a5   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x000002b5   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x00000385   Thumb Code   234  ddiv.o(.text)
    __aeabi_dcmple                           0x00000475   Thumb Code    44  dcmple.o(.text)
    __aeabi_i2d                              0x000004a1   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x000004c9   Thumb Code    24  dfltui.o(.text)
    __aeabi_d2iz                             0x000004e5   Thumb Code    62  dfixi.o(.text)
    __aeabi_uidiv                            0x0000052d   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x0000052d   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_uldivmod                         0x0000056b   Thumb Code    96  uldiv.o(.text)
    __aeabi_llsl                             0x000005cb   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x000005cb   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x000005eb   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x000005eb   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0000060d   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x0000060d   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x00000633   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x00000633   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x0000064d   Thumb Code   164  depilogue.o(.text)
    __aeabi_d2ulz                            0x000006f1   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x00000731   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x00000759   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000759   Thumb Code     0  init.o(.text)
    DL_ADC12_setClockConfig                  0x000008c1   Thumb Code    64  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x0000096d   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_SYSCTL_configSYSPLL                   0x00000b99   Thumb Code   192  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x00000cd5   Thumb Code    80  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00000d79   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_Timer_initFourCCPWMMode               0x00000dc9   Thumb Code   268  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_setCaptCompUpdateMethod         0x00000f01   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00000f1d   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00000f35   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00000f45   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00000ffd   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x000010d5   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    GROUP1_IRQHandler                        0x0000111b   Thumb Code    22  encoder_driver.o(.text.GROUP1_IRQHandler)
    Get_Analog_value                         0x00001131   Thumb Code   208  no_mcu_ganv_grayscale_sensor.o(.text.Get_Analog_value)
    Get_Anolog_Value                         0x00001205   Thumb Code    60  no_mcu_ganv_grayscale_sensor.o(.text.Get_Anolog_Value)
    Get_Digtal_For_User                      0x00001241   Thumb Code    14  no_mcu_ganv_grayscale_sensor.o(.text.Get_Digtal_For_User)
    Get_Normalize_For_User                   0x0000124f   Thumb Code    56  no_mcu_ganv_grayscale_sensor.o(.text.Get_Normalize_For_User)
    Motor_Create                             0x00001287   Thumb Code   126  motor_driver.o(.text.Motor_Create)
    Motor_Stop                               0x00001305   Thumb Code    86  motor_driver.o(.text.Motor_Stop)
    No_MCU_Ganv_Sensor_Init                  0x00001395   Thumb Code   388  no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init)
    No_MCU_Ganv_Sensor_Init_Frist            0x0000151d   Thumb Code   114  no_mcu_ganv_grayscale_sensor.o(.text.No_MCU_Ganv_Sensor_Init_Frist)
    No_Mcu_Ganv_Sensor_Task_Without_tick     0x0000158f   Thumb Code    66  no_mcu_ganv_grayscale_sensor.o(.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
    PID_Init                                 0x000015d1   Thumb Code   112  user_pid.o(.text.PID_Init)
    SYSCFG_DL_ADC_VOLTAGE_init               0x0000165d   Thumb Code    88  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    SYSCFG_DL_GPIO_init                      0x000016b9   Thumb Code   260  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_MOTOR_PWM_LEFT_init            0x000017c1   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init)
    SYSCFG_DL_MOTOR_PWM_RIGHT_init           0x00001829   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init)
    SYSCFG_DL_SYSCTL_CLK_init                0x00001891   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    SYSCFG_DL_SYSCTL_init                    0x000018ad   Thumb Code    84  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00001905   Thumb Code    12  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_UART_0_init                    0x00001915   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x00001959   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x0000198d   Thumb Code   100  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Handler                          0x00001a49   Thumb Code    12  scheduler.o(.text.SysTick_Handler)
    UART0_IRQHandler                         0x00001a55   Thumb Code    64  uart_driver.o(.text.UART0_IRQHandler)
    adc_getValue                             0x00001b85   Thumb Code    80  no_mcu_ganv_grayscale_sensor.o(.text.adc_getValue)
    convertAnalogToDigital                   0x00001bd5   Thumb Code   108  no_mcu_ganv_grayscale_sensor.o(.text.convertAnalogToDigital)
    delay_ms                                 0x00001c41   Thumb Code    22  time.o(.text.delay_ms)
    delay_us                                 0x00001c59   Thumb Code   108  time.o(.text.delay_us)
    encoder_config                           0x00001ccd   Thumb Code    24  encoder_driver.o(.text.encoder_config)
    encoder_init                             0x00001ded   Thumb Code    42  encoder_driver.o(.text.encoder_init)
    main                                     0x00001e17   Thumb Code    28  main.o(.text.main)
    my_printf                                0x00001e33   Thumb Code    58  uart_driver.o(.text.my_printf)
    normalizeAnalogValues                    0x00001e6d   Thumb Code   170  no_mcu_ganv_grayscale_sensor.o(.text.normalizeAnalogValues)
    pid_init                                 0x00001f17   Thumb Code    80  pid.o(.text.pid_init)
    pid_set_target                           0x00001f67   Thumb Code    16  pid.o(.text.pid_set_target)
    scheduler_init                           0x00001f79   Thumb Code    28  scheduler.o(.text.scheduler_init)
    scheduler_run                            0x00001f95   Thumb Code   104  scheduler.o(.text.scheduler_run)
    uart0_task                               0x00002005   Thumb Code   112  uart_driver.o(.text.uart0_task)
    uart_send_char                           0x0000208d   Thumb Code    40  uart_driver.o(.text.uart_send_char)
    uart_send_string                         0x000020b5   Thumb Code    44  uart_driver.o(.text.uart_send_string)
    user_config                              0x000020e1   Thumb Code    28  main.o(.text.user_config)
    user_gray_init                           0x000020fd   Thumb Code    20  gray_app.o(.text.user_gray_init)
    user_gray_task                           0x00002119   Thumb Code   196  gray_app.o(.text.user_gray_task)
    user_motor_init                          0x000021fd   Thumb Code    72  user_motor.o(.text.user_motor_init)
    __0vsnprintf                             0x000022a9   Thumb Code    44  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x000022a9   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x000022a9   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x000022a9   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x000022a9   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_clz                                0x000022d9   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x00002309   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00002319   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00002321   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x00002cb8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00002cd8   Number         0  anon$$obj.o(Region$$Table)
    basic_speed                              0x20200000   Data           4  user_pid.o(.data.basic_speed)
    black                                    0x20200004   Data          16  gray_app.o(.data.black)
    pid_params_left                          0x20200014   Data          20  user_pid.o(.data.pid_params_left)
    pid_params_line                          0x20200028   Data          20  user_pid.o(.data.pid_params_line)
    pid_params_right                         0x2020003c   Data          20  user_pid.o(.data.pid_params_right)
    white                                    0x20200068   Data          16  gray_app.o(.data.white)
    Anolog                                   0x20200078   Data          16  gray_app.o(.bss.Anolog)
    Digtal                                   0x20200088   Data           1  gray_app.o(.bss.Digtal)
    Normal                                   0x2020008a   Data          16  gray_app.o(.bss.Normal)
    encoder_get_count                        0x2020009a   Data           4  encoder_driver.o(.bss.encoder_get_count)
    encoder_left                             0x202000a0   Data          12  encoder_driver.o(.bss.encoder_left)
    encoder_right                            0x202000ac   Data          12  encoder_driver.o(.bss.encoder_right)
    gMOTOR_PWM_RIGHTBackup                   0x202000b8   Data         160  ti_msp_dl_config.o(.bss.gMOTOR_PWM_RIGHTBackup)
    left_motor                               0x20200158   Data          32  user_motor.o(.bss.left_motor)
    pid_line                                 0x20200178   Data          60  user_pid.o(.bss.pid_line)
    pid_runing                               0x202001b4   Data           1  user_pid.o(.bss.pid_runing)
    pid_speed_left                           0x202001b8   Data          60  user_pid.o(.bss.pid_speed_left)
    pid_speed_right                          0x202001f4   Data          60  user_pid.o(.bss.pid_speed_right)
    right_motor                              0x20200230   Data          32  user_motor.o(.bss.right_motor)
    sensor                                   0x20200250   Data         176  gray_app.o(.bss.sensor)
    task_num                                 0x20200300   Data           1  scheduler.o(.bss.task_num)
    uart_rx_buffer                           0x20200301   Data         128  uart_driver.o(.bss.uart_rx_buffer)
    uart_rx_index                            0x20200381   Data           1  uart_driver.o(.bss.uart_rx_index)
    uart_tick                                0x20200384   Data           4  uart_driver.o(.bss.uart_tick)
    uwTick                                   0x20200388   Data           4  scheduler.o(.bss.uwTick)
    __initial_sp                             0x20204390   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00002d50, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00002cd8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO            3    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO          617  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO          698    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO          701    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          703    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          705    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO          706    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          708    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          710    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO          699    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO            4    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x00000024   Code   RO          626    .text               mc_p.l(memcpya.o)
    0x0000010c   0x0000010c   0x00000024   Code   RO          628    .text               mc_p.l(memseta.o)
    0x00000130   0x00000130   0x0000001e   Code   RO          630    .text               mc_p.l(strncmp.o)
    0x0000014e   0x0000014e   0x00000002   PAD
    0x00000150   0x00000150   0x00000164   Code   RO          668    .text               mf_p.l(dadd.o)
    0x000002b4   0x000002b4   0x000000d0   Code   RO          670    .text               mf_p.l(dmul.o)
    0x00000384   0x00000384   0x000000f0   Code   RO          672    .text               mf_p.l(ddiv.o)
    0x00000474   0x00000474   0x0000002c   Code   RO          682    .text               mf_p.l(dcmple.o)
    0x000004a0   0x000004a0   0x00000028   Code   RO          686    .text               mf_p.l(dflti.o)
    0x000004c8   0x000004c8   0x0000001c   Code   RO          688    .text               mf_p.l(dfltui.o)
    0x000004e4   0x000004e4   0x00000048   Code   RO          694    .text               mf_p.l(dfixi.o)
    0x0000052c   0x0000052c   0x0000003e   Code   RO          721    .text               mc_p.l(uidiv_div0.o)
    0x0000056a   0x0000056a   0x00000060   Code   RO          727    .text               mc_p.l(uldiv.o)
    0x000005ca   0x000005ca   0x00000020   Code   RO          729    .text               mc_p.l(llshl.o)
    0x000005ea   0x000005ea   0x00000022   Code   RO          731    .text               mc_p.l(llushr.o)
    0x0000060c   0x0000060c   0x00000026   Code   RO          733    .text               mc_p.l(llsshr.o)
    0x00000632   0x00000632   0x00000000   Code   RO          735    .text               mc_p.l(iusefp.o)
    0x00000632   0x00000632   0x000000be   Code   RO          740    .text               mf_p.l(depilogue.o)
    0x000006f0   0x000006f0   0x00000040   Code   RO          744    .text               mf_p.l(dfixul.o)
    0x00000730   0x00000730   0x00000028   Code   RO          746    .text               mf_p.l(cdrcmple.o)
    0x00000758   0x00000758   0x00000030   Code   RO          748    .text               mc_p.l(init.o)
    0x00000788   0x00000788   0x0000004a   Code   RO          107    .text.DL_ADC12_configConversionMem  ti_msp_dl_config.o
    0x000007d2   0x000007d2   0x00000016   Code   RO          307    .text.DL_ADC12_disableConversions  no_mcu_ganv_grayscale_sensor.o
    0x000007e8   0x000007e8   0x00000016   Code   RO          113    .text.DL_ADC12_enableConversions  ti_msp_dl_config.o
    0x000007fe   0x000007fe   0x00000016   Code   RO          299    .text.DL_ADC12_enableConversions  no_mcu_ganv_grayscale_sensor.o
    0x00000814   0x00000814   0x00000014   Code   RO           49    .text.DL_ADC12_enablePower  ti_msp_dl_config.o
    0x00000828   0x00000828   0x00000030   Code   RO          309    .text.DL_ADC12_getMemResult  no_mcu_ganv_grayscale_sensor.o
    0x00000858   0x00000858   0x00000010   Code   RO          337    .text.DL_ADC12_getStatus  no_mcu_ganv_grayscale_sensor.o
    0x00000868   0x00000868   0x00000048   Code   RO          105    .text.DL_ADC12_initSingleSample  ti_msp_dl_config.o
    0x000008b0   0x000008b0   0x00000010   Code   RO           41    .text.DL_ADC12_reset  ti_msp_dl_config.o
    0x000008c0   0x000008c0   0x00000040   Code   RO          422    .text.DL_ADC12_setClockConfig  driverlib.a(dl_adc12.o)
    0x00000900   0x00000900   0x0000001e   Code   RO          109    .text.DL_ADC12_setPowerDownMode  ti_msp_dl_config.o
    0x0000091e   0x0000091e   0x00000002   PAD
    0x00000920   0x00000920   0x00000018   Code   RO          111    .text.DL_ADC12_setSampleTime0  ti_msp_dl_config.o
    0x00000938   0x00000938   0x00000018   Code   RO          301    .text.DL_ADC12_startConversion  no_mcu_ganv_grayscale_sensor.o
    0x00000950   0x00000950   0x0000001c   Code   RO          305    .text.DL_ADC12_stopConversion  no_mcu_ganv_grayscale_sensor.o
    0x0000096c   0x0000096c   0x0000000a   Code   RO          434    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x00000976   0x00000976   0x00000028   Code   RO          117    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x0000099e   0x0000099e   0x00000002   PAD
    0x000009a0   0x000009a0   0x00000018   Code   RO           67    .text.DL_GPIO_clearInterruptStatus  ti_msp_dl_config.o
    0x000009b8   0x000009b8   0x0000001c   Code   RO          216    .text.DL_GPIO_clearInterruptStatus  encoder_driver.o
    0x000009d4   0x000009d4   0x00000014   Code   RO           63    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x000009e8   0x000009e8   0x00000014   Code   RO          232    .text.DL_GPIO_clearPins  motor_driver.o
    0x000009fc   0x000009fc   0x00000014   Code   RO          315    .text.DL_GPIO_clearPins  no_mcu_ganv_grayscale_sensor.o
    0x00000a10   0x00000a10   0x0000001c   Code   RO           69    .text.DL_GPIO_enableInterrupt  ti_msp_dl_config.o
    0x00000a2c   0x00000a2c   0x00000018   Code   RO           55    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x00000a44   0x00000a44   0x00000018   Code   RO           43    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x00000a5c   0x00000a5c   0x00000018   Code   RO          212    .text.DL_GPIO_getEnabledInterruptStatus  encoder_driver.o
    0x00000a74   0x00000a74   0x00000030   Code   RO           61    .text.DL_GPIO_initDigitalInputFeatures  ti_msp_dl_config.o
    0x00000aa4   0x00000aa4   0x00000014   Code   RO           59    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x00000ab8   0x00000ab8   0x00000014   Code   RO           51    .text.DL_GPIO_initPeripheralAnalogFunction  ti_msp_dl_config.o
    0x00000acc   0x00000acc   0x0000001c   Code   RO           57    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x00000ae8   0x00000ae8   0x0000001c   Code   RO           53    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x00000b04   0x00000b04   0x00000016   Code   RO          214    .text.DL_GPIO_readPins  encoder_driver.o
    0x00000b1a   0x00000b1a   0x00000002   PAD
    0x00000b1c   0x00000b1c   0x00000018   Code   RO           35    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00000b34   0x00000b34   0x0000001c   Code   RO           65    .text.DL_GPIO_setLowerPinsPolarity  ti_msp_dl_config.o
    0x00000b50   0x00000b50   0x00000014   Code   RO          230    .text.DL_GPIO_setPins  motor_driver.o
    0x00000b64   0x00000b64   0x00000018   Code   RO          313    .text.DL_GPIO_setPins  no_mcu_ganv_grayscale_sensor.o
    0x00000b7c   0x00000b7c   0x0000001c   Code   RO          200    .text.DL_Interrupt_getPendingGroup  encoder_driver.o
    0x00000b98   0x00000b98   0x000000c0   Code   RO          584    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000c58   0x00000c58   0x0000000c   Code   RO           77    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00000c64   0x00000c64   0x00000014   Code   RO           79    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x00000c78   0x00000c78   0x00000010   Code   RO           83    .text.DL_SYSCTL_enableMFCLK  ti_msp_dl_config.o
    0x00000c88   0x00000c88   0x00000010   Code   RO           85    .text.DL_SYSCTL_enableMFPCLK  ti_msp_dl_config.o
    0x00000c98   0x00000c98   0x00000008   Code   RO           89    .text.DL_SYSCTL_getClockStatus  ti_msp_dl_config.o
    0x00000ca0   0x00000ca0   0x00000018   Code   RO           71    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x00000cb8   0x00000cb8   0x0000001c   Code   RO           73    .text.DL_SYSCTL_setFlashWaitState  ti_msp_dl_config.o
    0x00000cd4   0x00000cd4   0x00000050   Code   RO          598    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000d24   0x00000d24   0x00000020   Code   RO           87    .text.DL_SYSCTL_setMFPCLKSource  ti_msp_dl_config.o
    0x00000d44   0x00000d44   0x0000001c   Code   RO           75    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x00000d60   0x00000d60   0x00000018   Code   RO           81    .text.DL_SYSCTL_setULPCLKDivider  ti_msp_dl_config.o
    0x00000d78   0x00000d78   0x00000028   Code   RO          592    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000da0   0x00000da0   0x00000010   Code   RO           93    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x00000db0   0x00000db0   0x00000018   Code   RO           45    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x00000dc8   0x00000dc8   0x0000010c   Code   RO          523    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x00000ed4   0x00000ed4   0x00000018   Code   RO           37    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x00000eec   0x00000eec   0x00000014   Code   RO           95    .text.DL_Timer_setCCPDirection  ti_msp_dl_config.o
    0x00000f00   0x00000f00   0x0000001c   Code   RO          489    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00000f1c   0x00000f1c   0x00000018   Code   RO          497    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00000f34   0x00000f34   0x00000010   Code   RO          449    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00000f44   0x00000f44   0x0000001c   Code   RO          443    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00000f60   0x00000f60   0x0000003c   Code   RO           91    .text.DL_Timer_setCounterControl  ti_msp_dl_config.o
    0x00000f9c   0x00000f9c   0x00000016   Code   RO          103    .text.DL_UART_enable  ti_msp_dl_config.o
    0x00000fb2   0x00000fb2   0x00000002   PAD
    0x00000fb4   0x00000fb4   0x0000001c   Code   RO          101    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x00000fd0   0x00000fd0   0x00000018   Code   RO           47    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x00000fe8   0x00000fe8   0x00000012   Code   RO          165    .text.DL_UART_getPendingInterrupt  uart_driver.o
    0x00000ffa   0x00000ffa   0x00000002   PAD
    0x00000ffc   0x00000ffc   0x00000048   Code   RO          544    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00001044   0x00001044   0x00000018   Code   RO          155    .text.DL_UART_isBusy  uart_driver.o
    0x0000105c   0x0000105c   0x00000014   Code   RO          167    .text.DL_UART_receiveData  uart_driver.o
    0x00001070   0x00001070   0x00000018   Code   RO           39    .text.DL_UART_reset  ti_msp_dl_config.o
    0x00001088   0x00001088   0x0000004c   Code   RO           99    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x000010d4   0x000010d4   0x00000012   Code   RO          546    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x000010e6   0x000010e6   0x0000001e   Code   RO           97    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x00001104   0x00001104   0x00000016   Code   RO          157    .text.DL_UART_transmitData  uart_driver.o
    0x0000111a   0x0000111a   0x00000016   Code   RO          198    .text.GROUP1_IRQHandler  encoder_driver.o
    0x00001130   0x00001130   0x000000d4   Code   RO          311    .text.Get_Analog_value  no_mcu_ganv_grayscale_sensor.o
    0x00001204   0x00001204   0x0000003c   Code   RO          335    .text.Get_Anolog_Value  no_mcu_ganv_grayscale_sensor.o
    0x00001240   0x00001240   0x0000000e   Code   RO          331    .text.Get_Digtal_For_User  no_mcu_ganv_grayscale_sensor.o
    0x0000124e   0x0000124e   0x00000038   Code   RO          333    .text.Get_Normalize_For_User  no_mcu_ganv_grayscale_sensor.o
    0x00001286   0x00001286   0x0000007e   Code   RO          228    .text.Motor_Create  motor_driver.o
    0x00001304   0x00001304   0x00000056   Code   RO          242    .text.Motor_Stop    motor_driver.o
    0x0000135a   0x0000135a   0x0000003a   Code   RO          236    .text.Motor_ValidateParams  motor_driver.o
    0x00001394   0x00001394   0x00000188   Code   RO          323    .text.No_MCU_Ganv_Sensor_Init  no_mcu_ganv_grayscale_sensor.o
    0x0000151c   0x0000151c   0x00000072   Code   RO          321    .text.No_MCU_Ganv_Sensor_Init_Frist  no_mcu_ganv_grayscale_sensor.o
    0x0000158e   0x0000158e   0x00000042   Code   RO          325    .text.No_Mcu_Ganv_Sensor_Task_Without_tick  no_mcu_ganv_grayscale_sensor.o
    0x000015d0   0x000015d0   0x0000008c   Code   RO          400    .text.PID_Init      user_pid.o
    0x0000165c   0x0000165c   0x0000005c   Code   RO           25    .text.SYSCFG_DL_ADC_VOLTAGE_init  ti_msp_dl_config.o
    0x000016b8   0x000016b8   0x00000108   Code   RO           15    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x000017c0   0x000017c0   0x00000068   Code   RO           19    .text.SYSCFG_DL_MOTOR_PWM_LEFT_init  ti_msp_dl_config.o
    0x00001828   0x00001828   0x00000068   Code   RO           21    .text.SYSCFG_DL_MOTOR_PWM_RIGHT_init  ti_msp_dl_config.o
    0x00001890   0x00001890   0x0000001c   Code   RO           29    .text.SYSCFG_DL_SYSCTL_CLK_init  ti_msp_dl_config.o
    0x000018ac   0x000018ac   0x00000058   Code   RO           17    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00001904   0x00001904   0x00000010   Code   RO           27    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001914   0x00001914   0x00000044   Code   RO           23    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00001958   0x00001958   0x00000034   Code   RO           11    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x0000198c   0x0000198c   0x00000074   Code   RO           13    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00001a00   0x00001a00   0x00000048   Code   RO          115    .text.SysTick_Config  ti_msp_dl_config.o
    0x00001a48   0x00001a48   0x0000000c   Code   RO          350    .text.SysTick_Handler  scheduler.o
    0x00001a54   0x00001a54   0x00000054   Code   RO          163    .text.UART0_IRQHandler  uart_driver.o
    0x00001aa8   0x00001aa8   0x0000002c   Code   RO          141    .text.__NVIC_ClearPendingIRQ  main.o
    0x00001ad4   0x00001ad4   0x0000002c   Code   RO          143    .text.__NVIC_EnableIRQ  main.o
    0x00001b00   0x00001b00   0x00000084   Code   RO          119    .text.__NVIC_SetPriority  ti_msp_dl_config.o
    0x00001b84   0x00001b84   0x00000050   Code   RO          297    .text.adc_getValue  no_mcu_ganv_grayscale_sensor.o
    0x00001bd4   0x00001bd4   0x0000006c   Code   RO          317    .text.convertAnalogToDigital  no_mcu_ganv_grayscale_sensor.o
    0x00001c40   0x00001c40   0x00000016   Code   RO          288    .text.delay_ms      time.o
    0x00001c56   0x00001c56   0x00000002   PAD
    0x00001c58   0x00001c58   0x00000074   Code   RO          286    .text.delay_us      time.o
    0x00001ccc   0x00001ccc   0x00000020   Code   RO          210    .text.encoder_config  encoder_driver.o
    0x00001cec   0x00001cec   0x00000100   Code   RO          202    .text.encoder_func  encoder_driver.o
    0x00001dec   0x00001dec   0x0000002a   Code   RO          204    .text.encoder_init  encoder_driver.o
    0x00001e16   0x00001e16   0x0000001c   Code   RO          137    .text.main          main.o
    0x00001e32   0x00001e32   0x0000003a   Code   RO          161    .text.my_printf     uart_driver.o
    0x00001e6c   0x00001e6c   0x000000aa   Code   RO          319    .text.normalizeAnalogValues  no_mcu_ganv_grayscale_sensor.o
    0x00001f16   0x00001f16   0x00000050   Code   RO          255    .text.pid_init      pid.o
    0x00001f66   0x00001f66   0x00000010   Code   RO          257    .text.pid_set_target  pid.o
    0x00001f76   0x00001f76   0x00000002   PAD
    0x00001f78   0x00001f78   0x0000001c   Code   RO          346    .text.scheduler_init  scheduler.o
    0x00001f94   0x00001f94   0x00000070   Code   RO          348    .text.scheduler_run  scheduler.o
    0x00002004   0x00002004   0x00000088   Code   RO          169    .text.uart0_task    uart_driver.o
    0x0000208c   0x0000208c   0x00000028   Code   RO          153    .text.uart_send_char  uart_driver.o
    0x000020b4   0x000020b4   0x0000002c   Code   RO          159    .text.uart_send_string  uart_driver.o
    0x000020e0   0x000020e0   0x0000001c   Code   RO          139    .text.user_config   main.o
    0x000020fc   0x000020fc   0x0000001c   Code   RO          364    .text.user_gray_init  gray_app.o
    0x00002118   0x00002118   0x000000e4   Code   RO          368    .text.user_gray_task  gray_app.o
    0x000021fc   0x000021fc   0x00000060   Code   RO          385    .text.user_motor_init  user_motor.o
    0x0000225c   0x0000225c   0x0000004c   Code   RO          303    .text.wait_idle_with_timeout  no_mcu_ganv_grayscale_sensor.o
    0x000022a8   0x000022a8   0x00000030   Code   RO          640    i.__0vsnprintf      mc_p.l(printfa.o)
    0x000022d8   0x000022d8   0x0000002e   Code   RO          742    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00002306   0x00002306   0x00000002   PAD
    0x00002308   0x00002308   0x0000000e   Code   RO          754    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00002316   0x00002316   0x00000002   PAD
    0x00002318   0x00002318   0x00000002   Code   RO          755    i.__scatterload_null  mc_p.l(handlers.o)
    0x0000231a   0x0000231a   0x00000006   PAD
    0x00002320   0x00002320   0x0000000e   Code   RO          756    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x0000232e   0x0000232e   0x00000002   PAD
    0x00002330   0x00002330   0x00000174   Code   RO          642    i._fp_digits        mc_p.l(printfa.o)
    0x000024a4   0x000024a4   0x000006ec   Code   RO          643    i._printf_core      mc_p.l(printfa.o)
    0x00002b90   0x00002b90   0x00000020   Code   RO          644    i._printf_post_padding  mc_p.l(printfa.o)
    0x00002bb0   0x00002bb0   0x0000002c   Code   RO          645    i._printf_pre_padding  mc_p.l(printfa.o)
    0x00002bdc   0x00002bdc   0x00000016   Code   RO          646    i._snputc           mc_p.l(printfa.o)
    0x00002bf2   0x00002bf2   0x00000002   PAD
    0x00002bf4   0x00002bf4   0x00000008   Data   RO          129    .rodata.gADC_VOLTAGEClockConfig  ti_msp_dl_config.o
    0x00002bfc   0x00002bfc   0x00000003   Data   RO          123    .rodata.gMOTOR_PWM_LEFTClockConfig  ti_msp_dl_config.o
    0x00002bff   0x00002bff   0x00000001   PAD
    0x00002c00   0x00002c00   0x00000008   Data   RO          124    .rodata.gMOTOR_PWM_LEFTConfig  ti_msp_dl_config.o
    0x00002c08   0x00002c08   0x00000003   Data   RO          125    .rodata.gMOTOR_PWM_RIGHTClockConfig  ti_msp_dl_config.o
    0x00002c0b   0x00002c0b   0x00000001   PAD
    0x00002c0c   0x00002c0c   0x00000008   Data   RO          126    .rodata.gMOTOR_PWM_RIGHTConfig  ti_msp_dl_config.o
    0x00002c14   0x00002c14   0x00000028   Data   RO          122    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00002c3c   0x00002c3c   0x00000002   Data   RO          127    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x00002c3e   0x00002c3e   0x0000000a   Data   RO          128    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x00002c48   0x00002c48   0x0000000b   Data   RO          174    .rodata.str1.1      uart_driver.o
    0x00002c53   0x00002c53   0x00000064   Data   RO          375    .rodata.str1.1      gray_app.o
    0x00002cb7   0x00002cb7   0x00000001   PAD
    0x00002cb8   0x00002cb8   0x00000020   Data   RO          753    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00002cd8, Size: 0x00004390, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00002cd8   0x00000004   Data   RW          406    .data.basic_speed   user_pid.o
    0x20200004   0x00002cdc   0x00000010   Data   RW          371    .data.black         gray_app.o
    0x20200014   0x00002cec   0x00000014   Data   RW          408    .data.pid_params_left  user_pid.o
    0x20200028   0x00002d00   0x00000014   Data   RW          410    .data.pid_params_line  user_pid.o
    0x2020003c   0x00002d14   0x00000014   Data   RW          409    .data.pid_params_right  user_pid.o
    0x20200050   0x00002d28   0x00000018   Data   RW          356    .data.scheduler_task  scheduler.o
    0x20200068   0x00002d40   0x00000010   Data   RW          372    .data.white         gray_app.o
    0x20200078        -       0x00000010   Zero   RW          370    .bss.Anolog         gray_app.o
    0x20200088        -       0x00000001   Zero   RW          373    .bss.Digtal         gray_app.o
    0x20200089   0x00002d50   0x00000001   PAD
    0x2020008a        -       0x00000010   Zero   RW          376    .bss.Normal         gray_app.o
    0x2020009a        -       0x00000004   Zero   RW          218    .bss.encoder_get_count  encoder_driver.o
    0x2020009e   0x00002d50   0x00000002   PAD
    0x202000a0        -       0x0000000c   Zero   RW          219    .bss.encoder_left   encoder_driver.o
    0x202000ac        -       0x0000000c   Zero   RW          220    .bss.encoder_right  encoder_driver.o
    0x202000b8        -       0x000000a0   Zero   RW          121    .bss.gMOTOR_PWM_RIGHTBackup  ti_msp_dl_config.o
    0x20200158        -       0x00000020   Zero   RW          391    .bss.left_motor     user_motor.o
    0x20200178        -       0x0000003c   Zero   RW          413    .bss.pid_line       user_pid.o
    0x202001b4        -       0x00000001   Zero   RW          407    .bss.pid_runing     user_pid.o
    0x202001b5   0x00002d50   0x00000003   PAD
    0x202001b8        -       0x0000003c   Zero   RW          411    .bss.pid_speed_left  user_pid.o
    0x202001f4        -       0x0000003c   Zero   RW          412    .bss.pid_speed_right  user_pid.o
    0x20200230        -       0x00000020   Zero   RW          392    .bss.right_motor    user_motor.o
    0x20200250        -       0x000000b0   Zero   RW          374    .bss.sensor         gray_app.o
    0x20200300        -       0x00000001   Zero   RW          355    .bss.task_num       scheduler.o
    0x20200301        -       0x00000080   Zero   RW          171    .bss.uart_rx_buffer  uart_driver.o
    0x20200381        -       0x00000001   Zero   RW          172    .bss.uart_rx_index  uart_driver.o
    0x20200382   0x00002d50   0x00000002   PAD
    0x20200384        -       0x00000004   Zero   RW          173    .bss.uart_tick      uart_driver.o
    0x20200388        -       0x00000004   Zero   RW          354    .bss.uwTick         scheduler.o
    0x2020038c   0x00002d50   0x00000004   PAD
    0x20200390        -       0x00004000   Zero   RW            1    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       454         28          0          0         28       5936   encoder_driver.o
       256         40        100         32        209       3673   gray_app.o
       144          8          0          0          0       2419   main.o
       310          0          0          0          0       8211   motor_driver.o
      1552         24          0          0          0       9903   no_mcu_ganv_grayscale_sensor.o
        96          0          0          0          0       3017   pid.o
       152          8          0         24          5       1434   scheduler.o
        20          4        192          0      16384        616   startup_mspm0g350x_uvision.o
      2306        200         82          0        160      30454   ti_msp_dl_config.o
       138          8          0          0          0       1156   time.o
       446         52         11          0        133       4956   uart_driver.o
        96         24          0          0         64       6306   user_motor.o
       140         28          0         64        181       3790   user_pid.o

    ----------------------------------------------------------------------
      6124        <USER>        <GROUP>        120      17176      81871   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          3          0         12          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        64          8          0          0          0       4722   dl_adc12.o
        10          0          0          0          0        803   dl_common.o
       312         24          0          0          0      12877   dl_sysctl_mspm0g1x0x_g3x0x.o
       364         40          0          0          0      41557   dl_timer.o
        90          8          0          0          0      14163   dl_uart.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0         60   memcpya.o
        36          0          0          0          0        100   memseta.o
      2290         94          0          0          0        460   printfa.o
        30          0          0          0          0         72   strncmp.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       356          4          0          0          0        140   dadd.o
        44          0          0          0          0         68   dcmple.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        72         10          0          0          0         72   dfixi.o
        64         10          0          0          0         68   dfixul.o
        40          6          0          0          0         68   dflti.o
        28          4          0          0          0         68   dfltui.o
       208          6          0          0          0         88   dmul.o

    ----------------------------------------------------------------------
      4936        <USER>          <GROUP>          0          0      76182   Library Totals
        16          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       840         80          0          0          0      74122   driverlib.a
      2752        112          0          0          0       1120   mc_p.l
      1328         48          0          0          0        940   mf_p.l

    ----------------------------------------------------------------------
      4936        <USER>          <GROUP>          0          0      76182   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     11060        664        420        120      17176     156821   Grand Totals
     11060        664        420        120      17176     156821   ELF Image Totals
     11060        664        420        120          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                11480 (  11.21kB)
    Total RW  Size (RW Data + ZI Data)             17296 (  16.89kB)
    Total ROM Size (Code + RO Data + RW Data)      11600 (  11.33kB)

==============================================================================

