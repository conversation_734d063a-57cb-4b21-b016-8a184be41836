#include "bsp_system.h"
/**
 * @brief 用户配置函数声明
 */
void user_config(void);
void uart_send_string(UART_Regs *uart,char* str,int lenth);

/**
 * @brief 主函数
 * 
 * @param none
 * 
 * @return int 
 */
int main(void)
{
		//系统配置
    SYSCFG_DL_init();
		//用户配置
    user_config();
		//调度器初始化
    scheduler_init();

    while (1)
    {
			scheduler_run();
		}
}
/**
 * @brief 用户配置项
 * 
 * @param none
 * 
 * @return none
 */
void user_config(void)
{
    #ifdef UART_0_INST_INT_IRQN
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
    #endif

    #ifdef UART_1_INST_INT_IRQN
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART_1_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_1_INST_INT_IRQN);
    #endif

    #ifdef UART_2_INST_INT_IRQN
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART_2_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_2_INST_INT_IRQN);
    #endif

    #ifdef UART_3_INST_INT_IRQN
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART_3_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_3_INST_INT_IRQN);
    #endif
		
		#ifdef ENCODER_INT_IRQN
		//开启编码器中断
		NVIC_EnableIRQ(ENCODER_INT_IRQN);
		#endif
    
}
