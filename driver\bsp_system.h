#ifndef __BSP_SYSTEM_H
#define __BSP_SYSTEM_H

#include "stdio.h"
#include "stdarg.h"
#include "string.h"
/*驱动头文件*/
#include "ti_msp_dl_config.h"
#include "uart_driver.h"
#include "button_driver.h"
#include "encoder_driver.h"
#include "motor_driver.h"
#include "pid.h"
#include "bno08x_hal.h"
#include "Time.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "gray_detection.h"
#include "nchd12.h"
#include "soft_i2c.h"

/*用户头文件*/
#include "scheduler.h"
#include "gray_app.h"
#include "user_motor.h"
#include "user_pid.h"
extern uint32_t uwTick;    //系统时间
extern float g_line_position_error;
#endif