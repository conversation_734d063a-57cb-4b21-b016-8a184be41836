# MSPM0G3507 智能小车项目引脚配置文档

## 项目概述
本项目基于TI MSPM0G3507微控制器开发的智能小车系统，包含电机控制、编码器反馈、灰度传感器、OLED显示、UART通信等功能模块。

## 微控制器信息
- **芯片型号**: MSPM0G3507
- **封装**: 64-pin
- **主频**: 80MHz
- **架构**: ARM Cortex-M0+

## 引脚配置详情

### 1. 电源与时钟引脚
| 引脚名称 | 引脚号 | 功能描述 | 配置 |
|---------|--------|----------|------|
| PA5 | 10 | HFXIN - 外部高频晶振输入 | 模拟功能 |
| PA6 | 11 | HFXOUT - 外部高频晶振输出 | 模拟功能 |

### 2. 调试接口引脚
| 引脚名称 | 引脚号 | 功能描述 | 配置 |
|---------|--------|----------|------|
| PA19 | - | SWDIO - 串行调试数据 | 调试接口 |
| PA20 | - | SWCLK - 串行调试时钟 | 调试接口 |

### 3. 电机控制引脚

#### 3.1 左电机控制
| 引脚名称 | 引脚号 | GPIO | 功能描述 | 配置 |
|---------|--------|------|----------|------|
| PB11 | 28 | GPIO_MOTOR_PWM_LEFT_C1 | 左电机PWM控制 | PWM输出(TIMG8_CCP1) |
| PA22 | 47 | MOTOR_DIR_LEFT1 | 左电机方向控制1 | 数字输出 |
| PB24 | 52 | MOTOR_DIR_LEFT2 | 左电机方向控制2 | 数字输出 |

#### 3.2 右电机控制
| 引脚名称 | 引脚号 | GPIO | 功能描述 | 配置 |
|---------|--------|------|----------|------|
| PA31 | 6 | GPIO_MOTOR_PWM_RIGHT_C1 | 右电机PWM控制 | PWM输出(TIMG7_CCP1) |
| PA24 | 54 | MOTOR_DIR_RIGHT1 | 右电机方向控制1 | 数字输出 |
| PA26 | 59 | MOTOR_DIR_RIGHT2 | 右电机方向控制2 | 数字输出 |

### 4. 编码器接口引脚
| 引脚名称 | 引脚号 | GPIO | 功能描述 | 配置 |
|---------|--------|------|----------|------|
| PB6 | 23 | ENCODER_left_a | 左编码器A相 | 数字输入，下拉，上升沿中断 |
| PB7 | 24 | ENCODER_left_b | 左编码器B相 | 数字输入，上拉，下降沿中断 |
| PB8 | 25 | ENCODER_right_a | 右编码器A相 | 数字输入，下拉，上升沿中断 |
| PB9 | 26 | ENCODER_right_b | 右编码器B相 | 数字输入，上拉，下降沿中断 |

### 5. 灰度传感器引脚

#### 5.1 I2C通信引脚
| 引脚名称 | 引脚号 | GPIO | 功能描述 | 配置 |
|---------|--------|------|----------|------|
| - | - | I2C0_SDA | I2C数据线 | I2C功能(需要在syscfg中配置) |
| - | - | I2C0_SCL | I2C时钟线 | I2C功能(需要在syscfg中配置) |

#### 5.2 地址选择引脚
| 引脚名称 | 引脚号 | GPIO | 功能描述 | 配置 |
|---------|--------|------|----------|------|
| PA0 | 1 | GRAY_PIN_5 | 灰度传感器地址位0 | 数字输出 |
| PA1 | 2 | GRAY_PIN_6 | 灰度传感器地址位1 | 数字输出 |
| PA28 | 3 | GRAY_PIN_7 | 灰度传感器地址位2 | 数字输出 |

### 6. UART通信引脚
| 引脚名称 | 引脚号 | GPIO | 功能描述 | 配置 |
|---------|--------|------|----------|------|
| PA10 | 21 | GPIO_UART_0_TX | UART发送 | UART0_TX |
| PA11 | 22 | GPIO_UART_0_RX | UART接收 | UART0_RX |

**UART配置参数:**
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无
- 时钟源: MFCLK (4MHz)

### 7. ADC采样引脚
| 引脚名称 | 引脚号 | GPIO | 功能描述 | 配置 |
|---------|--------|------|----------|------|
| PA27 | - | GPIO_ADC_VOLTAGE_C0 | 电压采样 | ADC0通道0 |

**ADC配置参数:**
- 分辨率: 12位
- 参考电压: VDDA (3.3V)
- 采样时间: 1us
- 时钟分频: 8

### 8. I2C设备地址配置

#### 8.1 灰度传感器
- **默认地址**: 0x4C
- **设备类型**: GW灰度传感器
- **通信协议**: I2C

#### 8.2 OLED显示屏
- **设备地址**: 0x3C
- **设备类型**: SSD1306 OLED
- **通信协议**: I2C

## 功能模块说明

### 1. 电机驱动模块
- 支持双电机独立控制
- PWM频率可调
- 正反转控制
- 速度闭环控制

### 2. 编码器模块
- 正交编码器接口
- 硬件中断处理
- 速度和位置反馈

### 3. 灰度传感器模块
- 8路灰度检测
- I2C通信接口
- 支持数字和模拟输出
- 可配置传感器地址

### 4. 显示模块
- 128x64 OLED显示
- I2C接口
- 支持中文显示

### 5. 通信模块
- UART串口通信
- 支持调试输出
- 波特率115200

## 注意事项

1. **I2C引脚配置**: 当前配置文件中I2C0的SDA和SCL引脚未在syscfg中明确配置，需要在empty.syscfg中添加I2C模块配置。

2. **GRAY_INST定义**: 代码中使用的GRAY_INST宏应该定义为I2C0，建议在ti_msp_dl_config.h中添加：
   ```c
   #define GRAY_INST I2C0
   ```

3. **引脚复用**: 确保引脚功能不冲突，特别是GPIO和外设功能的复用。

4. **电源管理**: 所有使用的GPIO端口(GPIOA、GPIOB)都已在代码中启用电源。

5. **中断配置**: 编码器引脚配置了GPIO中断，确保中断服务程序正确实现。

## 开发环境
- **IDE**: Keil MDK
- **SDK**: MSPM0 SDK **********
- **编译器**: ARM Compiler 6
- **调试器**: J-Link

## 引脚映射表(按引脚号排序)

| 引脚号 | 引脚名称 | GPIO | 功能描述 | 模块 |
|--------|----------|------|----------|------|
| 1 | PA0 | GRAY_PIN_5 | 灰度传感器地址位0 | 灰度传感器 |
| 2 | PA1 | GRAY_PIN_6 | 灰度传感器地址位1 | 灰度传感器 |
| 3 | PA28 | GRAY_PIN_7 | 灰度传感器地址位2 | 灰度传感器 |
| 6 | PA31 | PWM_RIGHT | 右电机PWM控制 | 电机驱动 |
| 10 | PA5 | HFXIN | 外部晶振输入 | 时钟系统 |
| 11 | PA6 | HFXOUT | 外部晶振输出 | 时钟系统 |
| 18 | PA22 | DIR_LEFT1 | 左电机方向1 | 电机驱动 |
| 21 | PA10 | UART_TX | UART发送 | 通信 |
| 22 | PA11 | UART_RX | UART接收 | 通信 |
| 23 | PB6 | ENC_LA | 左编码器A相 | 编码器 |
| 24 | PB7 | ENC_LB | 左编码器B相 | 编码器 |
| 25 | PB8 | ENC_RA | 右编码器A相 | 编码器 |
| 26 | PB9 | ENC_RB | 右编码器B相 | 编码器 |
| 28 | PB11 | PWM_LEFT | 左电机PWM控制 | 电机驱动 |
| 30 | PA26 | DIR_RIGHT2 | 右电机方向2 | 电机驱动 |
| 23 | PB24 | DIR_LEFT2 | 左电机方向2 | 电机驱动 |
| 25 | PA24 | DIR_RIGHT1 | 右电机方向1 | 电机驱动 |
| - | PA27 | ADC_CH0 | 电压采样 | ADC |

## 软件配置说明

### 1. 时钟配置
- **系统时钟**: 80MHz
- **外部晶振**: 配置为HFXT
- **PLL配置**: 4倍频
- **外设时钟**:
  - UART: 4MHz (MFCLK)
  - PWM: 20MHz
  - ADC: 时钟8分频

### 2. 中断配置
- **编码器中断**: GPIOB组中断
- **UART中断**: 接收中断使能
- **ADC中断**: ADC0中断使能
- **定时器中断**: SYSTICK中断，周期80000个时钟周期(1ms)

### 3. DMA配置
当前项目未使用DMA功能。

## 硬件连接建议

### 1. 电机驱动电路
```
左电机:
- PWM: PB11 → 电机驱动器PWM输入
- DIR1: PA22 → 电机驱动器方向控制1
- DIR2: PB24 → 电机驱动器方向控制2

右电机:
- PWM: PA31 → 电机驱动器PWM输入
- DIR1: PA24 → 电机驱动器方向控制1
- DIR2: PA26 → 电机驱动器方向控制2
```

### 2. 编码器连接
```
左编码器:
- A相: PB6 (上升沿触发)
- B相: PB7 (下降沿触发)

右编码器:
- A相: PB8 (上升沿触发)
- B相: PB9 (下降沿触发)
```

### 3. I2C设备连接
```
I2C总线:
- SDA: 需要在syscfg中配置具体引脚
- SCL: 需要在syscfg中配置具体引脚
- 上拉电阻: 4.7kΩ (外部)

连接设备:
- 灰度传感器 (地址: 0x4C)
- OLED显示屏 (地址: 0x3C)
```

### 4. 灰度传感器地址配置
```
地址选择引脚:
- PA0: 地址位0
- PA1: 地址位1
- PA28: 地址位2

通过这3个引脚可以配置传感器的I2C地址
```

## 代码结构说明

### 1. 目录结构
```
├── driver/          # 驱动层
│   ├── motor_driver.h/c      # 电机驱动
│   ├── encoder_driver.h/c    # 编码器驱动
│   ├── uart_driver.h/c       # UART驱动
│   ├── IIC.h/c              # I2C底层驱动
│   ├── hardware_iic.h/c     # I2C硬件抽象
│   ├── pid.h/c              # PID控制器
│   └── bsp_system.h         # 系统头文件
├── logic/           # 逻辑层
│   ├── OLED.h/c             # OLED显示逻辑
│   ├── gray_app.h/c         # 灰度传感器应用
│   ├── user_motor.h/c       # 电机控制逻辑
│   ├── user_pid.h/c         # PID应用逻辑
│   └── scheduler.h/c        # 任务调度
├── user/            # 用户层实现
├── keil/            # Keil工程文件
├── ti_msp_dl_config.h/c  # 硬件配置
└── empty.syscfg     # SysConfig配置文件
```

### 2. 主要文件说明
- `ti_msp_dl_config.h/c`: 硬件抽象层配置，包含所有引脚定义
- `motor_driver.h/c`: 电机PWM和方向控制驱动
- `encoder_driver.h/c`: 编码器中断处理和计数
- `uart_driver.h/c`: 串口通信驱动，支持printf重定向
- `OLED.h/c`: SSD1306 OLED显示驱动
- `gray_app.h/c`: 灰度传感器数据处理和线位置计算
- `IIC.h/c`: I2C底层通信协议实现
- `hardware_iic.h/c`: I2C设备操作封装

## 配置修改建议

### 1. 缺失的I2C配置
当前empty.syscfg文件中缺少I2C模块配置，建议添加：
```javascript
const I2C = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1 = I2C.addInstance();
I2C1.$name = "GRAY_INST";
I2C1.peripheral.$assign = "I2C0";
I2C1.peripheral.sdaPin.$assign = "PA0";  // 根据实际硬件连接
I2C1.peripheral.sclPin.$assign = "PA1";  // 根据实际硬件连接
```

### 2. 宏定义补充
建议在ti_msp_dl_config.h中添加：
```c
/* I2C Instance Definition */
#define GRAY_INST                                                              I2C0
#define GRAY_INST_IRQHandler                                        I2C0_IRQHandler
#define GRAY_INST_INT_IRQN                                            I2C0_INT_IRQn

/* I2C Pin Definitions */
#define GPIO_GRAY_I2C_SDA_PORT                                               GPIOA
#define GPIO_GRAY_I2C_SCL_PORT                                               GPIOA
#define GPIO_GRAY_I2C_SDA_PIN                                        DL_GPIO_PIN_0
#define GPIO_GRAY_I2C_SCL_PIN                                        DL_GPIO_PIN_1
```

## 使用说明

### 1. 编译环境配置
1. 安装Keil MDK 5.37或更高版本
2. 安装MSPM0 SDK **********
3. 配置工程路径指向正确的SDK位置

### 2. 硬件连接检查
1. 确认电机驱动器连接正确
2. 检查编码器信号线连接
3. 验证I2C设备地址和连接
4. 确认UART调试线连接

### 3. 功能测试步骤
1. **基础功能测试**:
   - 编译下载程序
   - 检查UART输出是否正常
   - 测试OLED显示功能

2. **电机测试**:
   - 测试左右电机PWM输出
   - 验证方向控制功能
   - 检查编码器反馈

3. **传感器测试**:
   - 测试灰度传感器I2C通信
   - 验证传感器数据读取
   - 检查线位置计算

### 4. 调试建议
1. 使用UART输出调试信息
2. 通过OLED显示关键参数
3. 使用J-Link调试器进行在线调试
4. 检查GPIO状态和中断触发

## 常见问题排除

### 1. I2C通信问题
**现象**: 灰度传感器或OLED无响应
**排查步骤**:
1. 检查I2C引脚配置是否正确
2. 确认设备地址是否匹配(灰度传感器0x4C，OLED 0x3C)
3. 检查上拉电阻是否连接(4.7kΩ)
4. 使用示波器检查SCL和SDA信号

### 2. 编码器计数异常
**现象**: 编码器计数不准确或无计数
**排查步骤**:
1. 检查编码器供电是否正常
2. 确认A、B相信号连接正确
3. 检查中断配置和处理函数
4. 验证上拉/下拉电阻配置

### 3. 电机控制异常
**现象**: 电机不转或转向错误
**排查步骤**:
1. 检查PWM信号输出
2. 确认方向控制引脚状态
3. 检查电机驱动器供电
4. 验证电机驱动器使能信号

### 4. UART通信问题
**现象**: 串口无输出或乱码
**排查步骤**:
1. 确认波特率设置(115200)
2. 检查TX、RX引脚连接
3. 验证串口工具配置
4. 检查时钟配置是否正确

## 性能参数

### 1. 系统性能
- **主频**: 80MHz
- **指令执行**: 单周期执行
- **中断响应时间**: < 12个时钟周期
- **GPIO翻转频率**: 最高40MHz

### 2. 外设性能
- **PWM分辨率**: 16位
- **PWM频率**: 可配置，典型值20kHz
- **ADC转换时间**: 1us + 转换时间
- **I2C速度**: 标准模式100kHz，快速模式400kHz
- **UART最高波特率**: 2Mbps

### 3. 功耗指标
- **运行模式**: 约50mA @ 80MHz
- **睡眠模式**: < 1mA
- **深度睡眠**: < 100uA
- **关断模式**: < 10uA

## 参考资料

### 1. 官方文档
- [MSPM0G3507 数据手册](https://www.ti.com/product/MSPM0G3507)
- [MSPM0 SDK 用户指南](https://dev.ti.com/tirex/explore/node?node=A__AF-zoDjjKHdJBJdJBJdJBJ__MSPM0-SDK__a.QVer.**********)
- [SysConfig 工具指南](https://dev.ti.com/sysconfig/)

### 2. 开发工具
- [Keil MDK](https://www.keil.com/mdk5/)
- [Code Composer Studio](https://www.ti.com/tool/CCSTUDIO)
- [J-Link 调试器](https://www.segger.com/products/debug-probes/j-link/)

### 3. 相关库文件
- TI DriverLib for MSPM0
- CMSIS Core for Cortex-M0+
- ARM CMSIS DSP Library

## 版本信息
- **文档版本**: v1.0
- **创建日期**: 2024年
- **最后更新**: 2024年
- **维护者**: 开发团队

## 联系信息
如有技术问题或建议，请通过以下方式联系：
- 项目仓库: [GitHub链接]
- 技术支持: [邮箱地址]
- 文档反馈: [反馈链接]

---
*本文档基于代码分析生成，详细描述了MSPM0G3507智能小车项目的完整引脚配置。*
*文档内容会随着项目更新而持续维护，请关注最新版本。*
