/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.22.0+3893"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12   = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121  = ADC12.addInstance();
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const gate8  = system.clockTree["MFPCLKGATE"];
gate8.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 4;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const mux12       = system.clockTree["SYSPLLMUX"];
mux12.inputSelect = "zSYSPLLMUX_HFCLK";

const pinFunction3     = system.clockTree["HFCLKEXT"];
pinFunction3.inputFreq = 40;

const pinFunction4        = system.clockTree["HFXT"];
pinFunction4.enable       = true;
pinFunction4.inputFreq    = 40;
pinFunction4.HFCLKMonitor = true;
pinFunction4.HFXTStartup  = 100;

ADC121.sampClkDiv                        = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.repeatMode                        = true;
ADC121.sampleTime0                       = "1us";
ADC121.$name                             = "ADC_VOLTAGE";
ADC121.adcMem0_name                      = "ADC_CH0";
ADC121.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.peripheral.$assign                = "ADC0";
ADC121.peripheral.adcPin0.$assign        = "PA27";
ADC121.adcPin0Config.hideOutputInversion = scripting.forceWrite(false);
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric14";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                              = "ENCODER";
GPIO1.port                               = "PORTB";
GPIO1.associatedPins.create(4);
GPIO1.associatedPins[0].$name            = "left_a";
GPIO1.associatedPins[0].direction        = "INPUT";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[0].interruptEn      = true;
GPIO1.associatedPins[0].polarity         = "RISE";
GPIO1.associatedPins[0].pin.$assign      = "PB6";
GPIO1.associatedPins[1].$name            = "left_b";
GPIO1.associatedPins[1].direction        = "INPUT";
GPIO1.associatedPins[1].internalResistor = "PULL_UP";
GPIO1.associatedPins[1].interruptEn      = true;
GPIO1.associatedPins[1].polarity         = "FALL";
GPIO1.associatedPins[1].pin.$assign      = "PB7";
GPIO1.associatedPins[2].$name            = "right_a";
GPIO1.associatedPins[2].direction        = "INPUT";
GPIO1.associatedPins[2].interruptEn      = true;
GPIO1.associatedPins[2].polarity         = "RISE";
GPIO1.associatedPins[2].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[2].pin.$assign      = "PB8";
GPIO1.associatedPins[3].$name            = "right_b";
GPIO1.associatedPins[3].direction        = "INPUT";
GPIO1.associatedPins[3].internalResistor = "PULL_UP";
GPIO1.associatedPins[3].interruptEn      = true;
GPIO1.associatedPins[3].polarity         = "FALL";
GPIO1.associatedPins[3].pin.$assign      = "PB9";

GPIO2.$name                         = "MOTOR_DIR_LEFT1";
GPIO2.associatedPins[0].$name       = "PIN_0";
GPIO2.associatedPins[0].pin.$assign = "PA22";

GPIO3.$name                         = "MOTOR_DIR_LEFT2";
GPIO3.associatedPins[0].$name       = "PIN_1";
GPIO3.associatedPins[0].pin.$assign = "PB24";

GPIO4.$name                         = "MOTOR_DIR_RIGHT1";
GPIO4.associatedPins[0].$name       = "PIN_2";
GPIO4.associatedPins[0].pin.$assign = "PA24";

GPIO5.$name                         = "MOTOR_DIR_RIGHT2";
GPIO5.associatedPins[0].$name       = "PIN_3";
GPIO5.associatedPins[0].pin.$assign = "PA26";

GPIO6.$name                         = "GRAY";
GPIO6.associatedPins.create(3);
GPIO6.associatedPins[0].$name       = "PIN_5";
GPIO6.associatedPins[0].pin.$assign = "PA0";
GPIO6.associatedPins[1].$name       = "PIN_6";
GPIO6.associatedPins[1].pin.$assign = "PA1";
GPIO6.associatedPins[2].$name       = "PIN_7";
GPIO6.associatedPins[2].pin.$assign = "PA28";

PWM1.timerStartTimer                    = true;
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.ccIndex                            = [1];
PWM1.$name                              = "MOTOR_PWM_LEFT";
PWM1.clockPrescale                      = 2;
PWM1.peripheral.$assign                 = "TIMG8";
PWM1.peripheral.ccp1Pin.$assign         = "PB11";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC0";

PWM2.$name                              = "MOTOR_PWM_RIGHT";
PWM2.timerStartTimer                    = true;
PWM2.pwmMode                            = "EDGE_ALIGN_UP";
PWM2.clockDivider                       = 2;
PWM2.clockPrescale                      = 2;
PWM2.ccIndex                            = [1];
PWM2.peripheral.$assign                 = "TIMG7";
PWM2.peripheral.ccp1Pin.$assign         = "PA31";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric13";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";

SYSCTL.clockTreeEn           = true;
SYSCTL.forceDefaultClkConfig = true;
SYSCTL.validateClkStatus     = true;

SYSTICK.periodEnable      = true;
SYSTICK.period            = 80000;
SYSTICK.interruptEnable   = true;
SYSTICK.interruptPriority = "0";
SYSTICK.systickEnable     = true;

UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.uartClkSrc               = "MFCLK";
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
