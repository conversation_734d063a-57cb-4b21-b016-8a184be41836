Dependencies for Project 'main', Target 'main': (DO NOT MODIFY !)
CompilerVersion: 6160000::V6.16::ARMCLANG
F (../empty.syscfg)(0x688A2884)()
F (startup_mspm0g350x_uvision.s)(0x688ACA34)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto -c

-gdwarf-3 -Wa,armasm,--pd,"__MICROLIB SETA 1"

-Wa,armasm,--pd,"__UVISION_VERSION SETA 534" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (../ti_msp_dl_config.h)(0x688A26F8)()
F (../ti_msp_dl_config.c)(0x688A288A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MD)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
F (..\user\main.c)(0x688AA044)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/main.o -MD)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\scheduler.h)(0x68844026)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\logic\user_motor.h)(0x6889FB12)
I (..\logic\user_pid.h)(0x688AC158)
F (..\user\uart_driver.c)(0x688AB954)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/uart_driver.o -MD)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\scheduler.h)(0x68844026)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\logic\user_motor.h)(0x6889FB12)
I (..\logic\user_pid.h)(0x688AC158)
F (..\user\button_driver.c)(0x688A1C92)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/button_driver.o -MD)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\scheduler.h)(0x68844026)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\logic\user_motor.h)(0x6889FB12)
I (..\logic\user_pid.h)(0x688AC158)
F (..\user\encoder_driver.c)(0x688ABF32)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/encoder_driver.o -MD)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\scheduler.h)(0x68844026)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\logic\user_motor.h)(0x6889FB12)
I (..\logic\user_pid.h)(0x688AC158)
F (..\user\motor_driver.c)(0x6889FD18)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/motor_driver.o -MD)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\scheduler.h)(0x68844026)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\logic\user_motor.h)(0x6889FB12)
I (..\logic\user_pid.h)(0x688AC158)
F (..\user\pid.c)(0x686B7490)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/pid.o -MD)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (..\driver\pid.h)(0x685FBBB6)
F (..\user\Time.c)(0x67D8DBCA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/time.o -MD)
I (..\driver\Time.h)(0x67D7B93C)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
F (..\user\No_Mcu_Ganv_Grayscale_Sensor.c)(0x688ADE40)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/no_mcu_ganv_grayscale_sensor.o -MD)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\scheduler.h)(0x68844026)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\logic\user_motor.h)(0x6889FB12)
I (..\logic\user_pid.h)(0x688AC158)
F (..\driver\bsp_system.h)(0x688C4E4A)()
F (..\driver\uart_driver.h)(0x688A1C7E)()
F (..\driver\button_driver.h)(0x6881990C)()
F (..\driver\encoder_driver.h)(0x688AB8E2)()
F (..\driver\motor_driver.h)(0x6889FB12)()
F (..\driver\pid.h)(0x685FBBB6)()
F (..\driver\Time.h)(0x67D7B93C)()
F (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)()
F (..\logic\scheduler.c)(0x688ADCA4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/scheduler.o -MD)
I (..\logic\scheduler.h)(0x68844026)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\logic\user_motor.h)(0x6889FB12)
I (..\logic\user_pid.h)(0x688AC158)
F (..\logic\scheduler.h)(0x68844026)()
F (..\logic\gray_app.c)(0x688AE14C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/gray_app.o -MD)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\scheduler.h)(0x68844026)
I (..\logic\user_motor.h)(0x6889FB12)
I (..\logic\user_pid.h)(0x688AC158)
F (..\logic\gray_app.h)(0x6889DD96)()
F (..\logic\user_motor.c)(0x688AB9B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/user_motor.o -MD)
I (..\logic\user_motor.h)(0x6889FB12)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\scheduler.h)(0x68844026)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\logic\user_pid.h)(0x688AC158)
F (..\logic\user_motor.h)(0x6889FB12)()
F (..\logic\user_pid.c)(0x688AC188)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I C:/TI/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include -I C:/TI/mspm0_sdk_2_05_01_00/source -I ../../ti_template -I ../user -I ../driver -I ../logic -I ../function -I ./Objects -I ../NV12driver

-D__UVISION_VERSION="534" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/user_pid.o -MD)
I (..\logic\user_pid.h)(0x688AC158)
I (..\driver\bsp_system.h)(0x688C4E4A)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdio.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdarg.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\string.h)(0x6035A4A8)
I (..\..\ti_template\ti_msp_dl_config.h)(0x688A26F8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\msp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\DeviceFamily.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdint.h)(0x6035A4A8)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stdbool.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x68823DFB)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_compat.h)(0x5EE13552)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\arm_acle.h)(0x6035A4A4)
I (C:\TI\mspm0_sdk_2_05_01_00\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x68823DFB)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_aes.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_iomux.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_oa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\driverlib.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_adc12.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\math.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_factoryregion.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_core.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aes.h)(0x68823DFC)
I (C:\The_door_of_world\one_chip_computer\Keil\ARM\ARMCLANG\include\stddef.h)(0x6035A4A8)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_aesadv.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_comp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_crcp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dac12.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_dma.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_flashctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_sysctl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpamp.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_gpio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_i2c.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_iwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lfss.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_keystorectl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_lcd.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mathacl.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_mcan.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_opa.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_common.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_a.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_rtc_b.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_scratchpad.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_spi.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_tamperio.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timera.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timer.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_timerg.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_trng.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_extend.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_uart_main.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_vref.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\dl_wwdt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_interrupt.h)(0x68823DFC)
I (C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\m0p\dl_systick.h)(0x68823DFC)
I (..\driver\uart_driver.h)(0x688A1C7E)
I (..\driver\button_driver.h)(0x6881990C)
I (..\driver\encoder_driver.h)(0x688AB8E2)
I (..\driver\motor_driver.h)(0x6889FB12)
I (..\driver\pid.h)(0x685FBBB6)
I (..\driver\bno08x_hal.h)(0x688448C4)
I (..\driver\Time.h)(0x67D7B93C)
I (..\driver\No_Mcu_Ganv_Grayscale_Sensor_Config.h)(0x68897B2C)
I (..\NV12driver\gray_detection.h)(0x674D1990)
I (..\NV12driver\nchd12.h)(0x66F2CA18)
I (..\NV12driver\soft_i2c.h)(0x66EC4594)
I (..\logic\scheduler.h)(0x68844026)
I (..\logic\gray_app.h)(0x6889DD96)
I (..\logic\user_motor.h)(0x6889FB12)
F (..\logic\user_pid.h)(0x688AC158)()
