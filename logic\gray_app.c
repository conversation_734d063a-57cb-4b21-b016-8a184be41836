#include "gray_app.h"

No_MCU_Sensor sensor;

unsigned short Anolog[8]={0};
unsigned short black[8]={2465,2793,88,113,1522,961,105,96};
unsigned short white[8]={3233,3236,3251,3234,3141,3138,3154,3114};
unsigned short Normal[8];
// 白：Anolog 3084-3058-3080-3085-3032-3041-3045-3028
// 黑：Anolog 105-92-88-113-95-91-105-96
// unsigned short black[8]={2613,2663,88,113,95,91,105,96};
//unsigned short user_blank[8]={0};
unsigned char Digtal = 0;

float g_line_position_error;

/**
 * @brief 灰度初始化函数
 * 
 * @param none
 * 
 * @return none
 */
void user_gray_init(void)
{
		//初始化传感器，不带黑白值
//		No_MCU_Ganv_Sensor_Init_Frist(&sensor);
//		No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
//		Get_Anolog_Value(&sensor,Anolog);
//		//此时打印的ADC的值，可用通过这个ADC作为黑白值的校准
//		my_printf(UART_0_INST,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",
//			Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
		//也可以自己写按键逻辑完成一键校准功能
		//得到黑白校准值之后，初始化传感器
		No_MCU_Ganv_Sensor_Init(&sensor,white,black);
	
		delay_ms(100);
}

void Get_Digtal_wei(unsigned char *sensor,unsigned char num)
{
	for(uint8_t i=0;i<8;i++)
	{
		sensor[i] = ~(num >> i) & (0x01);
	}
}

void user_gray_task(void)
{
//	//传感器主任务(无定时器版本)
//	No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
//	
//	//获取归一化状态
//	Get_Normalize_For_User(&sensor,Anolog);

//	Get_Digtal_wei(Normal,sensor.Digtal);
//	
//	my_printf(UART_0_INST,"Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",
//	Normal[0],Normal[1],Normal[2],Normal[3],Normal[4],Normal[5],Normal[6],Normal[7]);
	
    No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
    
    // 获取并打印原始ADC值
    Get_Anolog_Value(&sensor,Anolog);
    my_printf(UART_0_INST,"ADC: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
        Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
    
    // 获取数字量输出
    Digtal=~Get_Digtal_For_User(&sensor);
    my_printf(UART_0_INST,"Digital: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
        (Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,
        (Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
    
    // 获取归一化值
    if(Get_Normalize_For_User(&sensor,Normal))
    {
        my_printf(UART_0_INST,"Normal: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
            Normal[0],Normal[1],Normal[2],Normal[3],Normal[4],Normal[5],Normal[6],Normal[7]);
    }

//  No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
//	Get_Normalize_For_User(&sensor,Anolog);
//	//获取传感器数字量结果(只有当有黑白值传入进去了之后才会有这个值！！)
//	Digtal=~Get_Digtal_For_User(&sensor);
//	my_printf(UART_0_INST,"Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",
//		(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,
//		(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
	delay_ms(1);
//	Get_Anolog_Value(&sensor,Anolog);
//		//此时打印的ADC的值，可用通过这个ADC作为黑白值的校准
//		my_printf(UART_0_INST,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",
//			Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);

}


