#include "uart_driver.h"

#define UART_BUFFER_SIZE 128
/**
 * @brief 串口发送单个字符
 */
void uart_send_char(UART_Regs *uart,char ch)
{
    //当串口忙的时候等待，不忙的时候再发送传进来的字符
    while( DL_UART_isBusy(uart) == true );
    //发送单个字符
    DL_UART_Main_transmitData(uart, ch);
}
/**
 * @brief 串口发送字符串
 */
void uart_send_string(UART_Regs *uart,char* str,int lenth)
{
    //当前字符串地址不在结尾 并且 字符串首地址不为空
    while(lenth--)
    {
        //发送字符串首地址中的字符，并且在发送完成之后首地址自增
        uart_send_char(uart,*str++);
    }
}
/**
 * @brief 串口发送重定向
 */
int my_printf(UART_Regs *uart,const char *format,...)
{
	char buffer[UART_BUFFER_SIZE]; // 临时存储格式化后的字符串
	va_list arg;      // 处理可变参数
	int len;          // 最终字符串长度

	va_start(arg, format); 
	// 安全地格式化字符串到 buffer
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	// 通过 HAL 库发送 buffer 中的内容
	uart_send_string(uart, buffer, len);
	return len;
}

#ifdef UART_0_INST

#define TIME_OUT 10 //超时时间
uint8_t uart_rx_buffer[UART_BUFFER_SIZE];//串口0数据池
uint8_t uart_rx_index;///串口0数据池索引
uint32_t uart_tick;//串口0时间戳
/**
 * @brief 串口0中断服务函数
 */
void UART_0_INST_IRQHandler(void)
{
    //如果产生了串口中断
    switch( DL_UART_getPendingInterrupt(UART_0_INST) )
    {
        case DL_UART_IIDX_RX://接收中断
            //存储数据
            uart_rx_buffer[uart_rx_index++] = DL_UART_Main_receiveData(UART_0_INST);
            //获取时间戳
            uart_tick = uwTick;
            //防止数组越界
            if(uart_rx_index >= UART_BUFFER_SIZE)
                uart_rx_index = 0;
        break;

        default://其他的串口中断
                ;
        break;
    }
}
#endif

#ifdef UART_1_INST

#define TIME_OUT_1 10 //超时时间
uint8_t uart_rx_buffer_1[UART_BUFFER_SIZE];//串口1数据池
uint8_t uart_rx_index_1;///串口1数据池索引
uint32_t uart_tick_1;//串口1时间戳
/**
 * @brief 串口1中断服务函数
 */
void UART_1_INST_IRQHandler(void)
{
    //如果产生了串口中断
    switch(DL_UART_getPendingInterrupt(UART_1_INST) )
    {
        case DL_UART_IIDX_RX://接收中断
            //存储数据
            uart_rx_buffer_1[uart_rx_index_1++] = DL_UART_Main_receiveData(UART_1_INST);
            //获取时间戳
            uart_tick_1 = uwTick;
            //防止数组越界
            if(uart_rx_index_1 >= UART_BUFFER_SIZE)
                uart_rx_index_1 = 0;
        break;

        default://其他的串口中断
                ;
        break;
    }
}
#endif

#ifdef UART_2_INST

#define TIME_OUT_2 10 //超时时间
uint8_t uart_rx_buffer_2[UART_BUFFER_SIZE];//串口0数据池
uint8_t uart_rx_index_2;///串口0数据池索引
uint32_t uart_tick_2;//串口0时间戳
/**
 * @brief 串口0中断服务函数
 */
void UART_2_INST_IRQHandler(void)
{
    //如果产生了串口中断
    switch( DL_UART_getPendingInterrupt(UART_2_INST) )
    {
        case DL_UART_IIDX_RX://接收中断
            //存储数据
            uart_rx_buffer_2[uart_rx_index_2++] = DL_UART_Main_receiveData(UART_2_INST);
            //获取时间戳
            uart_tick_2 = uwTick;
            //防止数组越界
            if(uart_rx_index_2 >= UART_BUFFER_SIZE)
                uart_rx_index_2 = 0;
        break;

        default://其他的串口中断
                ;
        break;
    }
}
#endif

#ifdef UART_3_INST

#define TIME_OUT_3 10 //超时时间
uint8_t uart_rx_buffer_3[UART_BUFFER_SIZE];//串口0数据池
uint8_t uart_rx_index_3;///串口0数据池索引
uint32_t uart_tick_3;//串口0时间戳
/**
 * @brief 串口3中断服务函数
 */
void UART_3_INST_IRQHandler(void)
{
    //如果产生了串口中断
    switch( DL_UART_getPendingInterrupt(UART_3_INST) )
    {
        case DL_UART_IIDX_RX://接收中断
            //存储数据
            uart_rx_buffer_3[uart_rx_index_3++] = DL_UART_Main_receiveData(UART_3_INST);
            //获取时间戳
            uart_tick_3 = uwTick;
            //防止数组越界
            if(uart_rx_index_3 >= UART_BUFFER_SIZE)
                uart_rx_index_3 = 0;
        break;

        default://其他的串口中断
                ;
        break;
    }
}
#endif

extern uint8_t pid_runing;

/**
 * @brief 串口任务函数
 * @param none
 * @return none
 */
__WEAK void uart0_task(void)
{
    //如果没有数据则直接返回
    if(uart_rx_index == 0) 
        return;
    //查看是否超时
    if(uwTick - uart_tick >= TIME_OUT)
    {
        //进入解析
				
				if (strncmp((const char*)uart_rx_buffer, "start", 5) == 0)
				{
						pid_runing = 1;
				}
				else if (strncmp((const char*)uart_rx_buffer, "stop", 4) == 0)
				{
						pid_runing = 0;
						Motor_Stop(&left_motor);
						Motor_Stop(&right_motor);
				}
				
//        my_printf(UART_0_INST,"%s\r\n",uart_rx_buffer);
//				my_printf(UART_0_INST,"uart0 work succesful!!\r\n");
        //结束解析,清空buffer,用于下一次解析
        memset(uart_rx_buffer,0,sizeof(uart_rx_buffer));
        uart_rx_index = 0;
    }
		
}



