/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     80000000



/* Defines for MOTOR_PWM_LEFT */
#define MOTOR_PWM_LEFT_INST                                                TIMG8
#define MOTOR_PWM_LEFT_INST_IRQHandler                          TIMG8_IRQHandler
#define MOTOR_PWM_LEFT_INST_INT_IRQN                            (TIMG8_INT_IRQn)
#define MOTOR_PWM_LEFT_INST_CLK_FREQ                                    20000000
/* GPIO defines for channel 1 */
#define GPIO_MOTOR_PWM_LEFT_C1_PORT                                        GPIOB
#define GPIO_MOTOR_PWM_LEFT_C1_PIN                                DL_GPIO_PIN_11
#define GPIO_MOTOR_PWM_LEFT_C1_IOMUX                             (IOMUX_PINCM28)
#define GPIO_MOTOR_PWM_LEFT_C1_IOMUX_FUNC             IOMUX_PINCM28_PF_TIMG8_CCP1
#define GPIO_MOTOR_PWM_LEFT_C1_IDX                           DL_TIMER_CC_1_INDEX

/* Defines for MOTOR_PWM_RIGHT */
#define MOTOR_PWM_RIGHT_INST                                               TIMG7
#define MOTOR_PWM_RIGHT_INST_IRQHandler                         TIMG7_IRQHandler
#define MOTOR_PWM_RIGHT_INST_INT_IRQN                           (TIMG7_INT_IRQn)
#define MOTOR_PWM_RIGHT_INST_CLK_FREQ                                   20000000
/* GPIO defines for channel 1 */
#define GPIO_MOTOR_PWM_RIGHT_C1_PORT                                       GPIOA
#define GPIO_MOTOR_PWM_RIGHT_C1_PIN                               DL_GPIO_PIN_31
#define GPIO_MOTOR_PWM_RIGHT_C1_IOMUX                             (IOMUX_PINCM6)
#define GPIO_MOTOR_PWM_RIGHT_C1_IOMUX_FUNC              IOMUX_PINCM6_PF_TIMG7_CCP1
#define GPIO_MOTOR_PWM_RIGHT_C1_IDX                          DL_TIMER_CC_1_INDEX



/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                            4000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                (115200)
#define UART_0_IBRD_4_MHZ_115200_BAUD                                        (2)
#define UART_0_FBRD_4_MHZ_115200_BAUD                                       (11)





/* Defines for ADC_VOLTAGE */
#define ADC_VOLTAGE_INST                                                    ADC0
#define ADC_VOLTAGE_INST_IRQHandler                              ADC0_IRQHandler
#define ADC_VOLTAGE_INST_INT_IRQN                                (ADC0_INT_IRQn)
#define ADC_VOLTAGE_ADCMEM_ADC_CH0                            DL_ADC12_MEM_IDX_0
#define ADC_VOLTAGE_ADCMEM_ADC_CH0_REF           DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC_VOLTAGE_ADCMEM_ADC_CH0_REF_VOLTAGE_V                                     3.3
#define GPIO_ADC_VOLTAGE_C0_PORT                                           GPIOA
#define GPIO_ADC_VOLTAGE_C0_PIN                                   DL_GPIO_PIN_27



/* Port definition for Pin Group MOTOR_DIR_LEFT1 */
#define MOTOR_DIR_LEFT1_PORT                                             (GPIOA)

/* Defines for PIN_0: GPIOA.22 with pinCMx 47 on package pin 18 */
#define MOTOR_DIR_LEFT1_PIN_0_PIN                               (DL_GPIO_PIN_22)
#define MOTOR_DIR_LEFT1_PIN_0_IOMUX                              (IOMUX_PINCM47)
/* Port definition for Pin Group MOTOR_DIR_LEFT2 */
#define MOTOR_DIR_LEFT2_PORT                                             (GPIOB)

/* Defines for PIN_1: GPIOB.24 with pinCMx 52 on package pin 23 */
#define MOTOR_DIR_LEFT2_PIN_1_PIN                               (DL_GPIO_PIN_24)
#define MOTOR_DIR_LEFT2_PIN_1_IOMUX                              (IOMUX_PINCM52)
/* Port definition for Pin Group MOTOR_DIR_RIGHT1 */
#define MOTOR_DIR_RIGHT1_PORT                                            (GPIOA)

/* Defines for PIN_2: GPIOA.24 with pinCMx 54 on package pin 25 */
#define MOTOR_DIR_RIGHT1_PIN_2_PIN                              (DL_GPIO_PIN_24)
#define MOTOR_DIR_RIGHT1_PIN_2_IOMUX                             (IOMUX_PINCM54)
/* Port definition for Pin Group MOTOR_DIR_RIGHT2 */
#define MOTOR_DIR_RIGHT2_PORT                                            (GPIOA)

/* Defines for PIN_3: GPIOA.26 with pinCMx 59 on package pin 30 */
#define MOTOR_DIR_RIGHT2_PIN_3_PIN                              (DL_GPIO_PIN_26)
#define MOTOR_DIR_RIGHT2_PIN_3_IOMUX                             (IOMUX_PINCM59)
/* Port definition for Pin Group ENCODER */
#define ENCODER_PORT                                                     (GPIOB)

/* Defines for left_a: GPIOB.6 with pinCMx 23 on package pin 58 */
// pins affected by this interrupt request:["left_a","left_b","right_a","right_b"]
#define ENCODER_INT_IRQN                                        (GPIOB_INT_IRQn)
#define ENCODER_INT_IIDX                        (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define ENCODER_left_a_IIDX                                  (DL_GPIO_IIDX_DIO6)
#define ENCODER_left_a_PIN                                       (DL_GPIO_PIN_6)
#define ENCODER_left_a_IOMUX                                     (IOMUX_PINCM23)
/* Defines for left_b: GPIOB.7 with pinCMx 24 on package pin 59 */
#define ENCODER_left_b_IIDX                                  (DL_GPIO_IIDX_DIO7)
#define ENCODER_left_b_PIN                                       (DL_GPIO_PIN_7)
#define ENCODER_left_b_IOMUX                                     (IOMUX_PINCM24)
/* Defines for right_a: GPIOB.8 with pinCMx 25 on package pin 60 */
#define ENCODER_right_a_IIDX                                 (DL_GPIO_IIDX_DIO8)
#define ENCODER_right_a_PIN                                      (DL_GPIO_PIN_8)
#define ENCODER_right_a_IOMUX                                    (IOMUX_PINCM25)
/* Defines for right_b: GPIOB.9 with pinCMx 26 on package pin 61 */
#define ENCODER_right_b_IIDX                                 (DL_GPIO_IIDX_DIO9)
#define ENCODER_right_b_PIN                                      (DL_GPIO_PIN_9)
#define ENCODER_right_b_IOMUX                                    (IOMUX_PINCM26)
/* Port definition for Pin Group GRAY */
#define GRAY_PORT                                                        (GPIOA)

/* Defines for PIN_5: GPIOA.0 with pinCMx 1 on package pin 33 */
#define GRAY_PIN_5_PIN                                           (DL_GPIO_PIN_0)
#define GRAY_PIN_5_IOMUX                                          (IOMUX_PINCM1)
/* Defines for PIN_6: GPIOA.1 with pinCMx 2 on package pin 34 */
#define GRAY_PIN_6_PIN                                           (DL_GPIO_PIN_1)
#define GRAY_PIN_6_IOMUX                                          (IOMUX_PINCM2)
/* Defines for PIN_7: GPIOA.28 with pinCMx 3 on package pin 35 */
#define GRAY_PIN_7_PIN                                          (DL_GPIO_PIN_28)
#define GRAY_PIN_7_IOMUX                                          (IOMUX_PINCM3)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_SYSCTL_CLK_init(void);
void SYSCFG_DL_MOTOR_PWM_LEFT_init(void);
void SYSCFG_DL_MOTOR_PWM_RIGHT_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_ADC_VOLTAGE_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
